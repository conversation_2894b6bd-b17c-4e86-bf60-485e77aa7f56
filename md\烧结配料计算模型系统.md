# 烧结配料计算模型系统

# 基于客户端技术的烧结配料计算模型系统开发说明文档（C# + Python）

# 烧结配料计算模型系统开发说明文档（C# + Python SQP 协同架构）

## 一、项目概述

### 1.1 核心定位

基于 **SQP 二次序列规划算法**（已有 Python 实现），构建集**原料参数管理、多目标优化、结果可视化**于一体的烧结配料系统。参考界面原型设计，实现 **表格化参数录入、多方案对比输出**，支撑烧结生产的 “质量达标” 与 “成本最优” 双目标决策。

## 二、系统架构设计

在 C# 桌面客户端开发领域，以 WPF（Windows Presentation Foundation）为核心的技术栈具备强大的界面表现力和丰富的交互能力。结合其他关键技术，形成完整的架构体系，层级拆分同样采用经典的三层架构：



*   **表现层**：采用 WPF 构建桌面应用，利用 XAML 实现灵活的界面布局与数据绑定，搭配 MVVM（Model-View-ViewModel）模式分离视图与逻辑，提升代码的可维护性和可测试性。同时，使用.NET 8 的 WinUI 3 集成，可实现现代化的视觉风格，并支持 Windows 10/11 原生特性。

*   **业务逻辑层**：基于.NET 8 的高性能运行时，结合 MediatR 实现命令查询职责分离（CQRS），通过依赖注入（DI）管理服务生命周期，确保业务逻辑的清晰与解耦，增强系统扩展性。

*   **数据访问层**：采用 Entity Framework Core 作为 ORM 框架，支持 SQL Server、MySQL 等多种数据库，通过仓储模式（Repository Pattern）封装数据访问逻辑，保障数据操作的一致性与安全性。

此外，引入常用的辅助技术完善系统功能：使用 AutoMapper 处理对象映射，提升数据传输效率；通过 Serilog 实现结构化日志记录，便于系统运行监控与问题排查；借助 FluentValidation 进行数据验证，确保输入数据的合法性；采用 IdentityServer4 或 Azure Active Directory 实现安全的身份认证与授权机制，全方位构建稳定、高效的 WPF 桌面客户端应用。

### 2.2 交互逻辑



1.  **前端→后端**：提交原料参数（湿基成分、水分、烧损）、优化目标（成本 / 质量）、约束条件（成分范围、配比上下限）。

2.  **后端→算法层**：将参数转换为 Python 算法输入格式（JSON），通过 HTTP POST 调用 FastAPI 接口。

3.  **算法层→后端**：返回多方案结果（干配比、TFe、碱度、单位成本等）。

4.  **后端→前端**：格式化结果并渲染（表格、偏差分析图）。

## 三、前端功能设计（参考界面原型）

### 3.1 页面布局与模块

#### （1）**原料参数编辑页**（核心交互区）



*   **布局**：左侧原料列表（可新增 / 删除行），右侧表单展示选中原料的**湿基成分（TFe、SiO₂、CaO 等）、水分、烧损率、配比上下限**。

*   **交互**：


    *   表格行内编辑（支持批量修改湿基成分、水分）；

    *   实时校验：配比上下限需满足 `0 < MinRatio < MaxRatio < 100`，水分≤20% 等工艺约束。

#### （2）**目标与约束配置页**



*   **功能区**：


    *   **优化目标选择**：单选 “成本最优” 或 “质量最优”；

    *   **成分约束配置**：输入框组设置 TFe、SiO₂、碱度（Ro）等的**上下限**（如 `TFe: 58~62%`，`Ro: 1.8~2.2`）；

    *   **配比约束**：配置各原料配比的`Min/Max`（继承原料编辑页数据，支持覆写）。

#### （3）**结果展示页**（多方案对比）



*   **布局**：上侧 tabs 切换 “成本最优方案”“质量最优方案”，下侧表格展示详细指标。

*   **输出指标**（对应界面原型汇总行）：



| 指标类型      | 具体指标             | 计算公式（简化）                                           |
| --------- | ---------------- | -------------------------------------------------- |
| **原料配比**  | 干配比（%）           | `干配比 = 湿配比 × (1 - 水分%) / Σ(湿配比 × (1 - 水分%)) × 100` |
| **烧结矿成分** | TFe（%）           | `Σ(干配比_i × 干基TFe_i) / Σ干配比_i`                      |
|           | 碱度 Ro            | `CaO含量 / SiO₂含量`                                   |
| **单耗与成本** | 吨矿单耗（吨原料 / 吨烧结矿） | `Σ干配比_i`（干配比总和，因烧成量已折算）                            |
|           | 单位成本（元 / 吨烧结矿）   | `Σ(干配比_i × 原料单价_i)`                                |

## 四、后端功能设计

### 4.1 核心模块与接口

#### （1）**参数预处理模块**



*   功能：将前端输入的**湿基成分、水分**转换为**干基成分**（`干基成分 = 湿基成分 / (1 - 水分%)`），校验约束合理性（如碱度上下限是否冲突）。

*   接口：`POST /api/Param/Validate`（接收原料 + 目标参数，返回校验结果）。

#### （2）**算法调用模块**



*   功能：封装 Python SQP 算法服务调用，参数格式转换（C# → Python JSON），处理异步响应。

*   关键代码（C# 调用 Python API）：



```
var client = new HttpClient();

var requestBody = new {

&#x20;   raw\_materials = rawMaterials.Select(m => new {

&#x20;       name = m.Name,

&#x20;       dry\_component = new { TFe = m.DryTFe, SiO2 = m.DrySiO2, CaO = m.DryCaO }, // 预处理后的干基成分

&#x20;       loss\_rate = m.LossRate,

&#x20;       min\_ratio = m.MinRatio,

&#x20;       max\_ratio = m.MaxRatio

&#x20;   }),

&#x20;   target = new {

&#x20;       tfe\_min = target.TFe\_Min,

&#x20;       ro\_target = target.Ro\_Target,

&#x20;       optimize\_type = "cost" // 或 "quality"

&#x20;   }

};

var response = await client.PostAsJsonAsync("http://python-sqp-service/api/solve", requestBody);

var result = await response.Content.ReadFromJsonAsync\<SqpResult>(); // 包含多方案结果
```

#### （3）**结果处理与存储模块**



*   功能：将算法返回的**多方案结果**（成本最优、质量最优）存储至数据库，并关联原料、目标参数版本。

*   接口：`POST /api/Result/Save`（保存计算结果，返回历史记录 ID）。

## 五、计算与执行流程（结合 SQP 算法）

### 5.1 步骤拆解

#### **步骤 1：输入参数采集（前端驱动）**



*   **原料数据**：通过表格录入 **湿基成分（TFe、SiO₂、CaO、MgO、Al₂O₃）、水分、烧损率、单价、配比上下限**。

*   **优化目标**：单选 “成本最优”（优先降本）或 “质量最优”（优先达标）。

*   **约束条件**：


    *   成分约束：`TFe∈[Hmin, Hmax]`，`Ro∈[Rmin, Rmax]`，`MgO∈[1.5%, 3.0%]` 等；

    *   配比约束：`原料i配比∈[MinRatio_i, MaxRatio_i]`，且 `Σ湿配比=100%`。

#### **步骤 2：SQP 算法求解（Python 层）**



*   **算法逻辑**：

1.  构建目标函数（成本最优：`min Σ(配比_i × 单价_i)`；质量最优：`min Σ(成分偏差²)`）；

2.  加入约束（成分范围、配比范围、碱度公式 `Ro=CaO/SiO₂`）；

3.  通过 SQP 迭代求解，输出 **成本最优方案（1\~3 个）** 和 **质量最优方案（1\~3 个）**。

#### **步骤 3：结果解析与输出（后端→前端）**



*   **核心指标计算**（算法结果映射）：



| 算法输出字段          | 前端展示指标      | 映射关系             |
| --------------- | ----------- | ---------------- |
| `dry_ratios`    | 干配比（%）      | 直接映射，按原料顺序展示     |
| `sinter_tfe`    | TFe（%）      | 算法计算的烧结矿 TFe 含量  |
| `sinter_ro`     | 碱度 Ro       | 算法计算的 CaO/SiO₂比值 |
| `unit_cost`     | 单位成本（元 / 吨） | 算法计算的原料成本总和      |
| `deviation_tfe` | TFe 偏差（%）   | \`               |

#### **步骤 4：方案可视化（前端）**



*   **成本最优方案**：表格展示 **湿配比、单位成本、TFe、Ro**，支持排序对比（如示例）：



| 方案 | 湿配比（%）           | 成本（元 / 吨） | TFe（%） | Ro   |
| -- | ---------------- | --------- | ------ | ---- |
| 1  | 铁矿 A:45, 石灰石：15… | 320.5     | 54.2   | 1.78 |
| 2  | 铁矿 B:50, 白云石：10… | 325.1     | 53.8   | 1.75 |



*   **质量最优方案**：表格展示 **湿配比、TFe 偏差、Ro 偏差、MgO**，突出成分达标性：



| 方案 | 湿配比（%）          | TFe 偏差 (%) | Ro 偏差 (%) | MgO（%） |
| -- | --------------- | ---------- | --------- | ------ |
| 1  | 铁矿 C:40, 菱镁矿：8… | +0.1       | -0.05     | 2.3    |

## 六、界面原型映射与增强

### 6.1 原型功能落地



*   **原料表格**：前端实现**行内编辑 + 批量导入**（支持 Excel 粘贴），同步校验水分、烧损率合理性（如水分 > 20% 标红）。

*   **结果汇总行**：实时计算并展示 **混合料 TFe、CaO、SiO₂、Ro**（与界面原型 “混合料” 栏对齐）。

*   **报警逻辑**：若算法返回 “无可行解”（如低 TFe + 低 Ro），前端弹窗提示 “原料无法满足目标，建议调整约束或原料”。

## 七、部署与协同

### 7.1 算法服务部署



*   将已有 Python SQP 代码封装为 FastAPI 服务，暴露 `/api/solve` 接口，支持 Docker 容器化部署（便于与 C# 后端解耦）。

### 7.2 前后端联调



*   约定 JSON 参数格式（如原料干基成分、约束范围），确保 C# 与 Python 数据无缝对接。

通过 **表格化交互 + 算法协同 + 多方案对比**，本系统实现了烧结配料模型从 “理论公式” 到 “生产可用工具” 的落地，核心优势在于：



1.  **SQP 算法复用**：直接集成已有 Python 代码，避免重复开发；

2.  **界面贴合生产**：参考原型设计，降低操作门槛；

3.  **双目标输出**：同时提供成本和质量最优方案，适配不同生产场景。

> （注：文档部分内容可能由 AI 生成）