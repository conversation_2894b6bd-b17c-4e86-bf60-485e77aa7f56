using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace SinterBlendingSystem.Core.Models
{
    /// <summary>
    /// 优化目标模型
    /// </summary>
    public class OptimizationTarget : INotifyPropertyChanged
    {
        private double _tfe = 55.0;
        private double _r = 1.90;
        private double _mgo = 2.39;
        private double _al2o3 = 1.89;
        private OptimizationType _optimizationType = OptimizationType.CostOptimal;

        /// <summary>
        /// 目标TFe含量(%)
        /// </summary>
        [Range(50, 70, ErrorMessage = "目标TFe含量必须在50-70%之间")]
        public double TFe
        {
            get => _tfe;
            set
            {
                if (Math.Abs(_tfe - value) > 0.001)
                {
                    _tfe = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 目标碱度(CaO/SiO2)
        /// </summary>
        [Range(1.0, 3.0, ErrorMessage = "目标碱度必须在1.0-3.0之间")]
        public double R
        {
            get => _r;
            set
            {
                if (Math.Abs(_r - value) > 0.001)
                {
                    _r = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 目标MgO含量(%)
        /// </summary>
        [Range(1.0, 5.0, ErrorMessage = "目标MgO含量必须在1.0-5.0%之间")]
        public double MgO
        {
            get => _mgo;
            set
            {
                if (Math.Abs(_mgo - value) > 0.001)
                {
                    _mgo = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 目标Al2O3含量(%)
        /// </summary>
        [Range(1.0, 3.0, ErrorMessage = "目标Al2O3含量必须在1.0-3.0%之间")]
        public double Al2O3
        {
            get => _al2o3;
            set
            {
                if (Math.Abs(_al2o3 - value) > 0.001)
                {
                    _al2o3 = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 优化类型
        /// </summary>
        public OptimizationType OptimizationType
        {
            get => _optimizationType;
            set
            {
                if (_optimizationType != value)
                {
                    _optimizationType = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 验证目标参数的合理性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var errors = new List<string>();

            if (TFe < 50 || TFe > 70)
                errors.Add("TFe目标值超出合理范围(50-70%)");

            if (R < 1.0 || R > 3.0)
                errors.Add("碱度目标值超出合理范围(1.0-3.0)");

            if (MgO < 1.0 || MgO > 5.0)
                errors.Add("MgO目标值超出合理范围(1.0-5.0%)");

            if (Al2O3 < 1.0 || Al2O3 > 3.0)
                errors.Add("Al2O3目标值超出合理范围(1.0-3.0%)");

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors
            };
        }

        /// <summary>
        /// 克隆目标对象
        /// </summary>
        /// <returns>克隆的目标对象</returns>
        public OptimizationTarget Clone()
        {
            return new OptimizationTarget
            {
                TFe = this.TFe,
                R = this.R,
                MgO = this.MgO,
                Al2O3 = this.Al2O3,
                OptimizationType = this.OptimizationType
            };
        }
    }

    /// <summary>
    /// 优化类型枚举
    /// </summary>
    public enum OptimizationType
    {
        /// <summary>
        /// 成本最优
        /// </summary>
        CostOptimal,

        /// <summary>
        /// 质量最优
        /// </summary>
        QualityOptimal
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
    }
}
