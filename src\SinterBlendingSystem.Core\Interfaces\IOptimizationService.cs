using SinterBlendingSystem.Core.Models;

namespace SinterBlendingSystem.Core.Interfaces
{
    /// <summary>
    /// 优化服务接口
    /// </summary>
    public interface IOptimizationService
    {
        /// <summary>
        /// 执行优化计算
        /// </summary>
        /// <param name="materials">原料数据列表</param>
        /// <param name="target">优化目标</param>
        /// <param name="constraints">约束条件</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>优化结果</returns>
        Task<OptimizationResult> OptimizeAsync(
            IEnumerable<MaterialData> materials,
            OptimizationTarget target,
            ConstraintRanges constraints,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 执行多方案优化（同时计算成本最优和质量最优）
        /// </summary>
        /// <param name="materials">原料数据列表</param>
        /// <param name="target">优化目标</param>
        /// <param name="constraints">约束条件</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>多方案优化结果</returns>
        Task<MultiSolutionResult> OptimizeMultiSolutionAsync(
            IEnumerable<MaterialData> materials,
            OptimizationTarget target,
            ConstraintRanges constraints,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 验证参数有效性
        /// </summary>
        /// <param name="materials">原料数据列表</param>
        /// <param name="target">优化目标</param>
        /// <param name="constraints">约束条件</param>
        /// <returns>验证结果</returns>
        ValidationResult ValidateParameters(
            IEnumerable<MaterialData> materials,
            OptimizationTarget target,
            ConstraintRanges constraints);

        /// <summary>
        /// 计算混合料性质（根据当前配比）
        /// </summary>
        /// <param name="materials">原料数据列表</param>
        /// <returns>混合料性质</returns>
        SinterProperties CalculateMixedProperties(IEnumerable<MaterialData> materials);

        /// <summary>
        /// 计算单位成本
        /// </summary>
        /// <param name="materials">原料数据列表</param>
        /// <returns>单位成本(元/吨)</returns>
        double CalculateUnitCost(IEnumerable<MaterialData> materials);

        /// <summary>
        /// 检查服务健康状态
        /// </summary>
        /// <returns>健康状态</returns>
        Task<ServiceHealthStatus> CheckHealthAsync();

        /// <summary>
        /// 优化进度事件
        /// </summary>
        event EventHandler<OptimizationProgressEventArgs>? ProgressChanged;

        /// <summary>
        /// 优化完成事件
        /// </summary>
        event EventHandler<OptimizationCompletedEventArgs>? OptimizationCompleted;
    }

    /// <summary>
    /// 多方案优化结果
    /// </summary>
    public class MultiSolutionResult
    {
        /// <summary>
        /// 成本最优方案
        /// </summary>
        public OptimizationResult? CostOptimalSolution { get; set; }

        /// <summary>
        /// 质量最优方案
        /// </summary>
        public OptimizationResult? QualityOptimalSolution { get; set; }

        /// <summary>
        /// 是否有任何成功的方案
        /// </summary>
        public bool HasSuccessfulSolution => 
            (CostOptimalSolution?.Success == true) || (QualityOptimalSolution?.Success == true);

        /// <summary>
        /// 推荐方案
        /// </summary>
        public OptimizationResult? RecommendedSolution
        {
            get
            {
                if (CostOptimalSolution?.Success == true && QualityOptimalSolution?.Success == true)
                {
                    // 如果两个方案都成功，根据成本差异选择推荐方案
                    var costDiff = Math.Abs(CostOptimalSolution.UnitCost - QualityOptimalSolution.UnitCost);
                    return costDiff < 10 ? QualityOptimalSolution : CostOptimalSolution;
                }
                return CostOptimalSolution?.Success == true ? CostOptimalSolution : QualityOptimalSolution;
            }
        }
    }

    /// <summary>
    /// 服务健康状态
    /// </summary>
    public class ServiceHealthStatus
    {
        /// <summary>
        /// 是否健康
        /// </summary>
        public bool IsHealthy { get; set; }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 响应时间(毫秒)
        /// </summary>
        public double ResponseTime { get; set; }

        /// <summary>
        /// 检查时间
        /// </summary>
        public DateTime CheckTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 服务版本
        /// </summary>
        public string Version { get; set; } = string.Empty;
    }

    /// <summary>
    /// 优化进度事件参数
    /// </summary>
    public class OptimizationProgressEventArgs : EventArgs
    {
        /// <summary>
        /// 进度百分比(0-100)
        /// </summary>
        public int ProgressPercentage { get; set; }

        /// <summary>
        /// 当前状态描述
        /// </summary>
        public string StatusMessage { get; set; } = string.Empty;

        /// <summary>
        /// 当前迭代次数
        /// </summary>
        public int CurrentIteration { get; set; }

        /// <summary>
        /// 最大迭代次数
        /// </summary>
        public int MaxIterations { get; set; }

        /// <summary>
        /// 当前目标函数值
        /// </summary>
        public double CurrentObjectiveValue { get; set; }
    }

    /// <summary>
    /// 优化完成事件参数
    /// </summary>
    public class OptimizationCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// 优化结果
        /// </summary>
        public OptimizationResult Result { get; set; } = new();

        /// <summary>
        /// 计算耗时(毫秒)
        /// </summary>
        public double ElapsedTime { get; set; }

        /// <summary>
        /// 是否被取消
        /// </summary>
        public bool IsCancelled { get; set; }
    }
}
