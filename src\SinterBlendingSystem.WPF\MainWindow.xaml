<Window x:Class="SinterBlendingSystem.WPF.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="烧结配料系统 - SQP优化算法" 
        Height="800" Width="1200"
        MinHeight="600" MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <!-- 顶部操作栏 -->
                <RowDefinition Height="*"/>
                <!-- 主内容区 -->
                <RowDefinition Height="Auto"/>
                <!-- 底部状态栏 -->
            </Grid.RowDefinitions>

            <!-- 顶部操作栏 -->
            <Border Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" 
                    BorderBrush="{DynamicResource PrimaryHueDarkBrush}" BorderThickness="0,0,0,1">
                <Grid Height="60">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 左侧：应用标题和图标 -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="16,0">
                        <materialDesign:PackIcon Kind="Factory" Width="32" Height="32" 
                                               Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"/>
                        <TextBlock Text="烧结配料系统" FontSize="18" FontWeight="Medium" 
                                 Foreground="{DynamicResource PrimaryHueMidForegroundBrush}" 
                                 VerticalAlignment="Center" Margin="12,0,0,0"/>
                    </StackPanel>

                    <!-- 中间：主要操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Button x:Name="BtnOptimize" Style="{StaticResource MaterialDesignRaisedAccentButton}" 
                                Margin="8,0" Padding="16,8" Command="{Binding OptimizeCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="PlayCircle" Width="20" Height="20" Margin="0,0,8,0"/>
                                <TextBlock Text="开始优化"/>
                            </StackPanel>
                        </Button>
                        
                        <Button x:Name="BtnSave" Style="{StaticResource MaterialDesignRaisedButton}" 
                                Margin="8,0" Padding="16,8" Command="{Binding SaveCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentSave" Width="20" Height="20" Margin="0,0,8,0"/>
                                <TextBlock Text="保存配置"/>
                            </StackPanel>
                        </Button>
                        
                        <Button x:Name="BtnLoad" Style="{StaticResource MaterialDesignRaisedButton}" 
                                Margin="8,0" Padding="16,8" Command="{Binding LoadCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FolderOpen" Width="20" Height="20" Margin="0,0,8,0"/>
                                <TextBlock Text="加载配置"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>

                    <!-- 右侧：系统状态和设置 -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" Margin="16,0">
                        <!-- 服务状态指示器 -->
                        <Border CornerRadius="8" Padding="8,4" Margin="0,0,8,0">
                            <Border.Style>
                                <Style TargetType="Border">
                                    <Setter Property="Background" Value="Red"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding ServiceStatus.IsHealthy}" Value="True">
                                            <Setter Property="Background" Value="Green"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Border.Style>
                            <TextBlock Text="{Binding ServiceStatusText}" FontSize="10"
                                     Foreground="White" FontWeight="Medium"/>
                        </Border>
                        
                        <!-- 设置按钮 -->
                        <Button Style="{StaticResource MaterialDesignIconButton}" 
                                Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                                Command="{Binding SettingsCommand}">
                            <materialDesign:PackIcon Kind="Settings" Width="24" Height="24"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 主内容区 -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="200"/>
                    <!-- 左侧导航栏 -->
                    <ColumnDefinition Width="*"/>
                    <!-- 主内容区 -->
                </Grid.ColumnDefinitions>

                <!-- 左侧导航栏 -->
                <Border Grid.Column="0" Background="#2D2D30"
                        BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,0,1,0">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="0,8">
                            <!-- 原料管理 -->
                            <Expander Header="原料管理" IsExpanded="True" Margin="8,4"
                                      Foreground="#E0E0E0" Background="Transparent">
                                <StackPanel Margin="16,8,8,8">
                                    <Button x:Name="BtnMaterialList" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="原料列表" Command="{Binding NavigateCommand}" 
                                            CommandParameter="MaterialList"/>
                                    <Button x:Name="BtnMaterialEdit" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="原料编辑" Command="{Binding NavigateCommand}" 
                                            CommandParameter="MaterialEdit"/>
                                </StackPanel>
                            </Expander>

                            <!-- 优化配置 -->
                            <Expander Header="优化配置" IsExpanded="True" Margin="8,4"
                                      Foreground="#E0E0E0" Background="Transparent">
                                <StackPanel Margin="16,8,8,8">
                                    <Button x:Name="BtnTargetConfig" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="目标配置" Command="{Binding NavigateCommand}" 
                                            CommandParameter="TargetConfig"/>
                                    <Button x:Name="BtnConstraintConfig" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="约束配置" Command="{Binding NavigateCommand}" 
                                            CommandParameter="ConstraintConfig"/>
                                </StackPanel>
                            </Expander>

                            <!-- 结果展示 -->
                            <Expander Header="结果展示" IsExpanded="True" Margin="8,4"
                                      Foreground="#E0E0E0" Background="Transparent">
                                <StackPanel Margin="16,8,8,8">
                                    <Button x:Name="BtnResultComparison" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="方案对比" Command="{Binding NavigateCommand}" 
                                            CommandParameter="ResultComparison"/>
                                    <Button x:Name="BtnResultVisualization" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="结果可视化" Command="{Binding NavigateCommand}" 
                                            CommandParameter="ResultVisualization"/>
                                    <Button x:Name="BtnHistory" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="历史记录" Command="{Binding NavigateCommand}" 
                                            CommandParameter="History"/>
                                </StackPanel>
                            </Expander>

                            <!-- 系统工具 -->
                            <Expander Header="系统工具" Margin="8,4"
                                      Foreground="#E0E0E0" Background="Transparent">
                                <StackPanel Margin="16,8,8,8">
                                    <Button x:Name="BtnImportExport" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="导入导出" Command="{Binding NavigateCommand}" 
                                            CommandParameter="ImportExport"/>
                                    <Button x:Name="BtnSystemSettings" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="系统设置" Command="{Binding NavigateCommand}" 
                                            CommandParameter="SystemSettings"/>
                                </StackPanel>
                            </Expander>
                        </StackPanel>
                    </ScrollViewer>
                </Border>

                <!-- 主内容区 -->
                <Border Grid.Column="1" Background="{DynamicResource MaterialDesignPaper}">
                    <ContentControl x:Name="MainContentControl" Content="{Binding CurrentView}" 
                                  Margin="16"/>
                </Border>
            </Grid>

            <!-- 底部状态栏 -->
            <Border Grid.Row="2" Background="{DynamicResource MaterialDesignCardBackground}" 
                    BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,1,0,0">
                <Grid Height="32">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 左侧：状态信息 -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="16,0">
                        <TextBlock Text="{Binding StatusMessage}" FontSize="11" 
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </StackPanel>

                    <!-- 中间：进度条（优化时显示） -->
                    <ProgressBar Grid.Column="1" Width="200" Height="4" Margin="8,0" 
                               Value="{Binding OptimizationProgress}" 
                               Visibility="{Binding IsOptimizing, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                    <!-- 右侧：系统信息 -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" Margin="16,0">
                        <TextBlock Text="{Binding CurrentTime, StringFormat='yyyy-MM-dd HH:mm:ss'}" 
                                 FontSize="11" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </materialDesign:DialogHost>
</Window>


