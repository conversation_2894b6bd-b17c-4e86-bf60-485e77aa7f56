using SinterBlendingSystem.Core.Models;

namespace SinterBlendingSystem.Core.Interfaces
{
    /// <summary>
    /// 数据服务接口
    /// </summary>
    public interface IDataService
    {
        /// <summary>
        /// 获取所有原料数据
        /// </summary>
        /// <returns>原料数据列表</returns>
        Task<IEnumerable<MaterialData>> GetMaterialsAsync();

        /// <summary>
        /// 根据ID获取原料数据
        /// </summary>
        /// <param name="code">原料代码</param>
        /// <returns>原料数据</returns>
        Task<MaterialData?> GetMaterialByCodeAsync(string code);

        /// <summary>
        /// 保存原料数据
        /// </summary>
        /// <param name="material">原料数据</param>
        /// <returns>保存结果</returns>
        Task<bool> SaveMaterialAsync(MaterialData material);

        /// <summary>
        /// 批量保存原料数据
        /// </summary>
        /// <param name="materials">原料数据列表</param>
        /// <returns>保存结果</returns>
        Task<bool> SaveMaterialsAsync(IEnumerable<MaterialData> materials);

        /// <summary>
        /// 删除原料数据
        /// </summary>
        /// <param name="code">原料代码</param>
        /// <returns>删除结果</returns>
        Task<bool> DeleteMaterialAsync(string code);

        /// <summary>
        /// 批量删除原料数据
        /// </summary>
        /// <param name="codes">原料代码列表</param>
        /// <returns>删除结果</returns>
        Task<bool> DeleteMaterialsAsync(IEnumerable<string> codes);

        /// <summary>
        /// 获取优化目标配置
        /// </summary>
        /// <returns>优化目标</returns>
        Task<OptimizationTarget> GetOptimizationTargetAsync();

        /// <summary>
        /// 保存优化目标配置
        /// </summary>
        /// <param name="target">优化目标</param>
        /// <returns>保存结果</returns>
        Task<bool> SaveOptimizationTargetAsync(OptimizationTarget target);

        /// <summary>
        /// 获取约束条件配置
        /// </summary>
        /// <returns>约束条件</returns>
        Task<ConstraintRanges> GetConstraintRangesAsync();

        /// <summary>
        /// 保存约束条件配置
        /// </summary>
        /// <param name="constraints">约束条件</param>
        /// <returns>保存结果</returns>
        Task<bool> SaveConstraintRangesAsync(ConstraintRanges constraints);

        /// <summary>
        /// 获取优化历史记录
        /// </summary>
        /// <param name="pageIndex">页索引</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>优化历史记录</returns>
        Task<PagedResult<OptimizationResult>> GetOptimizationHistoryAsync(int pageIndex = 0, int pageSize = 20);

        /// <summary>
        /// 保存优化结果
        /// </summary>
        /// <param name="result">优化结果</param>
        /// <returns>保存结果</returns>
        Task<bool> SaveOptimizationResultAsync(OptimizationResult result);

        /// <summary>
        /// 删除优化历史记录
        /// </summary>
        /// <param name="timestamp">时间戳</param>
        /// <returns>删除结果</returns>
        Task<bool> DeleteOptimizationResultAsync(DateTime timestamp);

        /// <summary>
        /// 清空优化历史记录
        /// </summary>
        /// <returns>清空结果</returns>
        Task<bool> ClearOptimizationHistoryAsync();

        /// <summary>
        /// 导入原料数据（从Excel文件）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>导入结果</returns>
        Task<ImportResult> ImportMaterialsFromExcelAsync(string filePath);

        /// <summary>
        /// 导出原料数据（到Excel文件）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="materials">原料数据列表</param>
        /// <returns>导出结果</returns>
        Task<bool> ExportMaterialsToExcelAsync(string filePath, IEnumerable<MaterialData> materials);

        /// <summary>
        /// 导出优化结果（到Excel文件）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="result">优化结果</param>
        /// <returns>导出结果</returns>
        Task<bool> ExportOptimizationResultToExcelAsync(string filePath, OptimizationResult result);

        /// <summary>
        /// 获取默认原料数据
        /// </summary>
        /// <returns>默认原料数据列表</returns>
        Task<IEnumerable<MaterialData>> GetDefaultMaterialsAsync();

        /// <summary>
        /// 重置为默认数据
        /// </summary>
        /// <returns>重置结果</returns>
        Task<bool> ResetToDefaultDataAsync();

        /// <summary>
        /// 数据变更事件
        /// </summary>
        event EventHandler<DataChangedEventArgs>? DataChanged;
    }

    /// <summary>
    /// 分页结果
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    public class PagedResult<T>
    {
        /// <summary>
        /// 数据列表
        /// </summary>
        public IEnumerable<T> Items { get; set; } = Enumerable.Empty<T>();

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 页索引
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

        /// <summary>
        /// 是否有上一页
        /// </summary>
        public bool HasPreviousPage => PageIndex > 0;

        /// <summary>
        /// 是否有下一页
        /// </summary>
        public bool HasNextPage => PageIndex < TotalPages - 1;
    }

    /// <summary>
    /// 导入结果
    /// </summary>
    public class ImportResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 导入的原料数据列表
        /// </summary>
        public IEnumerable<MaterialData> Materials { get; set; } = Enumerable.Empty<MaterialData>();

        /// <summary>
        /// 成功导入的记录数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败的记录数
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// 错误信息列表
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 警告信息列表
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// 数据变更事件参数
    /// </summary>
    public class DataChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 变更类型
        /// </summary>
        public DataChangeType ChangeType { get; set; }

        /// <summary>
        /// 变更的数据类型
        /// </summary>
        public Type DataType { get; set; } = typeof(object);

        /// <summary>
        /// 变更的数据标识
        /// </summary>
        public string? DataId { get; set; }

        /// <summary>
        /// 变更描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 数据变更类型
    /// </summary>
    public enum DataChangeType
    {
        /// <summary>
        /// 添加
        /// </summary>
        Added,

        /// <summary>
        /// 更新
        /// </summary>
        Updated,

        /// <summary>
        /// 删除
        /// </summary>
        Deleted,

        /// <summary>
        /// 批量操作
        /// </summary>
        BatchOperation,

        /// <summary>
        /// 重置
        /// </summary>
        Reset
    }
}
