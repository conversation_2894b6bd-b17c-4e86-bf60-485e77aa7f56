<UserControl x:Class="SinterBlendingSystem.WPF.Views.ConstraintConfigView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,8">
            <TextBlock Text="约束条件配置" Style="{StaticResource TitleTextStyle}"/>
        </Border>

        <!-- 配置内容 -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="16">
                    
                    <!-- TFe约束 -->
                    <GroupBox Header="TFe含量约束(%)" Style="{StaticResource MaterialGroupBoxStyle}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBox Grid.Column="0" 
                                     Text="{Binding Constraints.TFeRange.Min, StringFormat=F2}" 
                                     materialDesign:HintAssist.Hint="最小值"
                                     Style="{StaticResource NumericTextBoxStyle}" 
                                     Margin="0,0,8,0"/>
                            
                            <TextBox Grid.Column="1" 
                                     Text="{Binding Constraints.TFeRange.Max, StringFormat=F2}" 
                                     materialDesign:HintAssist.Hint="最大值"
                                     Style="{StaticResource NumericTextBoxStyle}" 
                                     Margin="8,0,0,0"/>
                        </Grid>
                    </GroupBox>

                    <!-- 碱度约束 -->
                    <GroupBox Header="碱度约束" Style="{StaticResource MaterialGroupBoxStyle}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBox Grid.Column="0" 
                                     Text="{Binding Constraints.RRange.Min, StringFormat=F2}" 
                                     materialDesign:HintAssist.Hint="最小值"
                                     Style="{StaticResource NumericTextBoxStyle}" 
                                     Margin="0,0,8,0"/>
                            
                            <TextBox Grid.Column="1" 
                                     Text="{Binding Constraints.RRange.Max, StringFormat=F2}" 
                                     materialDesign:HintAssist.Hint="最大值"
                                     Style="{StaticResource NumericTextBoxStyle}" 
                                     Margin="8,0,0,0"/>
                        </Grid>
                    </GroupBox>

                    <!-- MgO约束 -->
                    <GroupBox Header="MgO含量约束(%)" Style="{StaticResource MaterialGroupBoxStyle}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBox Grid.Column="0" 
                                     Text="{Binding Constraints.MgORange.Min, StringFormat=F2}" 
                                     materialDesign:HintAssist.Hint="最小值"
                                     Style="{StaticResource NumericTextBoxStyle}" 
                                     Margin="0,0,8,0"/>
                            
                            <TextBox Grid.Column="1" 
                                     Text="{Binding Constraints.MgORange.Max, StringFormat=F2}" 
                                     materialDesign:HintAssist.Hint="最大值"
                                     Style="{StaticResource NumericTextBoxStyle}" 
                                     Margin="8,0,0,0"/>
                        </Grid>
                    </GroupBox>

                    <!-- Al2O3约束 -->
                    <GroupBox Header="Al2O3含量约束(%)" Style="{StaticResource MaterialGroupBoxStyle}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBox Grid.Column="0" 
                                     Text="{Binding Constraints.Al2O3Range.Min, StringFormat=F2}" 
                                     materialDesign:HintAssist.Hint="最小值"
                                     Style="{StaticResource NumericTextBoxStyle}" 
                                     Margin="0,0,8,0"/>
                            
                            <TextBox Grid.Column="1" 
                                     Text="{Binding Constraints.Al2O3Range.Max, StringFormat=F2}" 
                                     materialDesign:HintAssist.Hint="最大值"
                                     Style="{StaticResource NumericTextBoxStyle}" 
                                     Margin="8,0,0,0"/>
                        </Grid>
                    </GroupBox>

                    <!-- 成本约束 -->
                    <GroupBox Header="成本约束(元/吨)" Style="{StaticResource MaterialGroupBoxStyle}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBox Grid.Column="0" 
                                     Text="{Binding Constraints.CostRange.Min, StringFormat=F2}" 
                                     materialDesign:HintAssist.Hint="最小值"
                                     Style="{StaticResource NumericTextBoxStyle}" 
                                     Margin="0,0,8,0"/>
                            
                            <TextBox Grid.Column="1" 
                                     Text="{Binding Constraints.CostRange.Max, StringFormat=F2}" 
                                     materialDesign:HintAssist.Hint="最大值"
                                     Style="{StaticResource NumericTextBoxStyle}" 
                                     Margin="8,0,0,0"/>
                        </Grid>
                    </GroupBox>

                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- 操作按钮栏 -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}" Margin="0,8,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="验证配置" Command="{Binding ValidateConfigurationCommand}" 
                        Style="{StaticResource MaterialDesignOutlinedButton}" Margin="4"/>
                <Button Content="重置默认" Command="{Binding ResetToDefaultCommand}" 
                        Style="{StaticResource MaterialDesignOutlinedButton}" Margin="4"/>
                <Button Content="保存配置" Command="{Binding SaveConfigurationCommand}" 
                        Style="{StaticResource MaterialDesignRaisedAccentButton}" Margin="4"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
