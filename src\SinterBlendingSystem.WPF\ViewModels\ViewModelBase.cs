using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;

namespace SinterBlendingSystem.WPF.ViewModels
{
    /// <summary>
    /// ViewModel基类
    /// </summary>
    public abstract class ViewModelBase : ObservableObject, INotifyPropertyChanged
    {
        protected readonly ILogger _logger;
        private bool _isBusy;
        private string _busyMessage = string.Empty;
        private string _errorMessage = string.Empty;
        private bool _hasError;

        protected ViewModelBase(ILogger logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 是否忙碌状态
        /// </summary>
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value);
        }

        /// <summary>
        /// 忙碌状态消息
        /// </summary>
        public string BusyMessage
        {
            get => _busyMessage;
            set => SetProperty(ref _busyMessage, value);
        }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                SetProperty(ref _errorMessage, value);
                HasError = !string.IsNullOrEmpty(value);
            }
        }

        /// <summary>
        /// 是否有错误
        /// </summary>
        public bool HasError
        {
            get => _hasError;
            set => SetProperty(ref _hasError, value);
        }

        /// <summary>
        /// 清除错误命令
        /// </summary>
        public IRelayCommand ClearErrorCommand => new RelayCommand(ClearError);

        /// <summary>
        /// 清除错误
        /// </summary>
        protected virtual void ClearError()
        {
            ErrorMessage = string.Empty;
        }

        /// <summary>
        /// 设置忙碌状态
        /// </summary>
        /// <param name="isBusy">是否忙碌</param>
        /// <param name="message">状态消息</param>
        protected virtual void SetBusy(bool isBusy, string message = "")
        {
            IsBusy = isBusy;
            BusyMessage = message;
        }

        /// <summary>
        /// 处理异常
        /// </summary>
        /// <param name="ex">异常</param>
        /// <param name="userMessage">用户友好的错误消息</param>
        protected virtual void HandleException(Exception ex, string userMessage = "")
        {
            _logger.LogError(ex, "ViewModel异常：{Message}", ex.Message);
            
            ErrorMessage = string.IsNullOrEmpty(userMessage) 
                ? $"操作失败：{ex.Message}" 
                : userMessage;
            
            SetBusy(false);
        }

        /// <summary>
        /// 执行异步操作
        /// </summary>
        /// <param name="operation">异步操作</param>
        /// <param name="busyMessage">忙碌状态消息</param>
        /// <param name="errorMessage">错误消息</param>
        /// <returns></returns>
        protected async Task ExecuteAsync(Func<Task> operation, string busyMessage = "正在处理...", string errorMessage = "")
        {
            try
            {
                ClearError();
                SetBusy(true, busyMessage);
                
                await operation();
            }
            catch (Exception ex)
            {
                HandleException(ex, errorMessage);
            }
            finally
            {
                SetBusy(false);
            }
        }

        /// <summary>
        /// 执行异步操作并返回结果
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">异步操作</param>
        /// <param name="busyMessage">忙碌状态消息</param>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>操作结果</returns>
        protected async Task<T?> ExecuteAsync<T>(Func<Task<T>> operation, string busyMessage = "正在处理...", string errorMessage = "")
        {
            try
            {
                ClearError();
                SetBusy(true, busyMessage);
                
                return await operation();
            }
            catch (Exception ex)
            {
                HandleException(ex, errorMessage);
                return default;
            }
            finally
            {
                SetBusy(false);
            }
        }

        /// <summary>
        /// 验证属性值
        /// </summary>
        /// <param name="value">属性值</param>
        /// <param name="propertyName">属性名</param>
        /// <returns>验证结果</returns>
        protected virtual bool ValidateProperty(object? value, [System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            if (string.IsNullOrEmpty(propertyName))
                return true;

            var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
            var validationContext = new System.ComponentModel.DataAnnotations.ValidationContext(this)
            {
                MemberName = propertyName
            };

            var isValid = System.ComponentModel.DataAnnotations.Validator.TryValidateProperty(
                value, validationContext, validationResults);

            if (!isValid && validationResults.Any())
            {
                var errorMessage = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                SetError(propertyName, errorMessage);
            }
            else
            {
                ClearError(propertyName);
            }

            return isValid;
        }

        /// <summary>
        /// 设置属性错误
        /// </summary>
        /// <param name="propertyName">属性名</param>
        /// <param name="errorMessage">错误消息</param>
        protected virtual void SetError(string propertyName, string errorMessage)
        {
            // 可以在子类中重写以实现具体的错误处理逻辑
            _logger.LogWarning("属性验证失败：{PropertyName} - {ErrorMessage}", propertyName, errorMessage);
        }

        /// <summary>
        /// 清除属性错误
        /// </summary>
        /// <param name="propertyName">属性名</param>
        protected virtual void ClearError(string propertyName)
        {
            // 可以在子类中重写以实现具体的错误清除逻辑
        }

        /// <summary>
        /// 初始化ViewModel
        /// </summary>
        public virtual async Task InitializeAsync()
        {
            // 子类可以重写此方法进行初始化
            await Task.CompletedTask;
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public virtual void Cleanup()
        {
            // 子类可以重写此方法进行资源清理
        }
    }
}
