<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 主色调 -->
    <Color x:Key="PrimaryColor">#FF2196F3</Color>
    <Color x:Key="PrimaryDarkColor">#FF1976D2</Color>
    <Color x:Key="PrimaryLightColor">#FFBBDEFB</Color>
    
    <!-- 辅助色调 -->
    <Color x:Key="AccentColor">#FF4CAF50</Color>
    <Color x:Key="AccentDarkColor">#FF388E3C</Color>
    <Color x:Key="AccentLightColor">#FFC8E6C9</Color>
    
    <!-- 状态颜色 -->
    <Color x:Key="SuccessColor">#FF4CAF50</Color>
    <Color x:Key="WarningColor">#FFFF9800</Color>
    <Color x:Key="ErrorColor">#FFF44336</Color>
    <Color x:Key="InfoColor">#FF2196F3</Color>
    
    <!-- 中性色 -->
    <Color x:Key="BackgroundColor">#FFFAFAFA</Color>
    <Color x:Key="SurfaceColor">#FFFFFFFF</Color>
    <Color x:Key="BorderColor">#FFE0E0E0</Color>
    <Color x:Key="DividerColor">#FFBDBDBD</Color>
    
    <!-- 文本颜色 -->
    <Color x:Key="TextPrimaryColor">#FF212121</Color>
    <Color x:Key="TextSecondaryColor">#FF757575</Color>
    <Color x:Key="TextDisabledColor">#FFBDBDBD</Color>
    <Color x:Key="TextOnPrimaryColor">#FFFFFFFF</Color>
    
    <!-- 数据网格颜色 -->
    <Color x:Key="GridHeaderColor">#FFF5F5F5</Color>
    <Color x:Key="GridAlternateRowColor">#FFFAFAFA</Color>
    <Color x:Key="GridSelectedRowColor">#FFE3F2FD</Color>
    <Color x:Key="GridHoverRowColor">#FFF0F0F0</Color>
    
    <!-- 原料状态颜色 -->
    <Color x:Key="MaterialNormalColor">#FF4CAF50</Color>
    <Color x:Key="MaterialWarningColor">#FFFF9800</Color>
    <Color x:Key="MaterialErrorColor">#FFF44336</Color>
    <Color x:Key="MaterialEditingColor">#FF2196F3</Color>
    <Color x:Key="MaterialLockedColor">#FF9E9E9E</Color>

    <!-- 画刷定义 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryDarkColor}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryLightColor}"/>
    
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
    <SolidColorBrush x:Key="AccentDarkBrush" Color="{StaticResource AccentDarkColor}"/>
    <SolidColorBrush x:Key="AccentLightBrush" Color="{StaticResource AccentLightColor}"/>
    
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor}"/>
    
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
    <SolidColorBrush x:Key="BorderBrush" Color="{StaticResource BorderColor}"/>
    <SolidColorBrush x:Key="DividerBrush" Color="{StaticResource DividerColor}"/>
    
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimaryColor}"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource TextSecondaryColor}"/>
    <SolidColorBrush x:Key="TextDisabledBrush" Color="{StaticResource TextDisabledColor}"/>
    <SolidColorBrush x:Key="TextOnPrimaryBrush" Color="{StaticResource TextOnPrimaryColor}"/>
    
    <SolidColorBrush x:Key="GridHeaderBrush" Color="{StaticResource GridHeaderColor}"/>
    <SolidColorBrush x:Key="GridAlternateRowBrush" Color="{StaticResource GridAlternateRowColor}"/>
    <SolidColorBrush x:Key="GridSelectedRowBrush" Color="{StaticResource GridSelectedRowColor}"/>
    <SolidColorBrush x:Key="GridHoverRowBrush" Color="{StaticResource GridHoverRowColor}"/>
    
    <SolidColorBrush x:Key="MaterialNormalBrush" Color="{StaticResource MaterialNormalColor}"/>
    <SolidColorBrush x:Key="MaterialWarningBrush" Color="{StaticResource MaterialWarningColor}"/>
    <SolidColorBrush x:Key="MaterialErrorBrush" Color="{StaticResource MaterialErrorColor}"/>
    <SolidColorBrush x:Key="MaterialEditingBrush" Color="{StaticResource MaterialEditingColor}"/>
    <SolidColorBrush x:Key="MaterialLockedBrush" Color="{StaticResource MaterialLockedColor}"/>

    <!-- 渐变画刷 -->
    <LinearGradientBrush x:Key="HeaderGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="{StaticResource PrimaryLightColor}" Offset="0"/>
        <GradientStop Color="{StaticResource PrimaryColor}" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="ButtonGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#FFFFFFFF" Offset="0"/>
        <GradientStop Color="#FFF0F0F0" Offset="1"/>
    </LinearGradientBrush>

    <!-- 阴影效果 -->
    <DropShadowEffect x:Key="CardShadow" 
                      Color="#40000000" 
                      Direction="270" 
                      ShadowDepth="2" 
                      BlurRadius="8" 
                      Opacity="0.3"/>
    
    <DropShadowEffect x:Key="ButtonShadow" 
                      Color="#30000000" 
                      Direction="270" 
                      ShadowDepth="1" 
                      BlurRadius="4" 
                      Opacity="0.2"/>

</ResourceDictionary>
