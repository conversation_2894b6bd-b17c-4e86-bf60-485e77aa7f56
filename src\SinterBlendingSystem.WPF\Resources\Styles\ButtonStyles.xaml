<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- 主要按钮样式 -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextOnPrimaryBrush}"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Effect" Value="{StaticResource ButtonShadow}"/>
    </Style>

    <!-- 次要按钮样式 -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 危险按钮样式 -->
    <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 成功按钮样式 -->
    <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 工具栏按钮样式 -->
    <Style x:Key="ToolBarButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Margin" Value="2"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="MinWidth" Value="32"/>
    </Style>

    <!-- 图标按钮样式 -->
    <Style x:Key="IconButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignIconButton}">
        <Setter Property="Width" Value="40"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 浮动操作按钮样式 -->
    <Style x:Key="FloatingActionButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFloatingActionButton}">
        <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Width" Value="56"/>
        <Setter Property="Height" Value="56"/>
        <Setter Property="Effect" Value="{StaticResource CardShadow}"/>
    </Style>

    <!-- 小型浮动操作按钮样式 -->
    <Style x:Key="MiniFloatingActionButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFloatingActionMiniButton}">
        <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Width" Value="40"/>
        <Setter Property="Height" Value="40"/>
    </Style>

    <!-- 切换按钮样式 -->
    <Style x:Key="ToggleButtonStyle" TargetType="ToggleButton" BasedOn="{StaticResource MaterialDesignFlatPrimaryToggleButton}">
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 分割按钮样式 -->
    <Style x:Key="SplitButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Margin" Value="0"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

</ResourceDictionary>
