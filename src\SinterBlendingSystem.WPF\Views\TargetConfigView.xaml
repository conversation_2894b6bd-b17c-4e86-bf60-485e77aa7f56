<UserControl x:Class="SinterBlendingSystem.WPF.Views.TargetConfigView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,8">
            <TextBlock Text="优化目标配置" Style="{StaticResource TitleTextStyle}"/>
        </Border>

        <!-- 配置内容 -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="16">
                    
                    <!-- 优化类型选择 -->
                    <GroupBox Header="优化类型" Style="{StaticResource MaterialGroupBoxStyle}">
                        <StackPanel>
                            <RadioButton Content="成本最优" IsChecked="True" Margin="0,4"/>
                            <RadioButton Content="质量最优" IsChecked="False" Margin="0,4"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- 目标成分配置 -->
                    <GroupBox Header="目标成分" Style="{StaticResource MaterialGroupBoxStyle}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- TFe目标 -->
                            <TextBox Grid.Row="0" Grid.Column="0" 
                                     Text="{Binding Target.TFe, StringFormat=F2}" 
                                     materialDesign:HintAssist.Hint="TFe目标值(%)"
                                     Style="{StaticResource NumericTextBoxStyle}" 
                                     Margin="0,0,8,8"/>

                            <!-- 碱度目标 -->
                            <TextBox Grid.Row="0" Grid.Column="1" 
                                     Text="{Binding Target.R, StringFormat=F2}" 
                                     materialDesign:HintAssist.Hint="碱度目标值"
                                     Style="{StaticResource NumericTextBoxStyle}" 
                                     Margin="8,0,0,8"/>

                            <!-- MgO目标 -->
                            <TextBox Grid.Row="1" Grid.Column="0" 
                                     Text="{Binding Target.MgO, StringFormat=F2}" 
                                     materialDesign:HintAssist.Hint="MgO目标值(%)"
                                     Style="{StaticResource NumericTextBoxStyle}" 
                                     Margin="0,0,8,8"/>

                            <!-- Al2O3目标 -->
                            <TextBox Grid.Row="1" Grid.Column="1" 
                                     Text="{Binding Target.Al2O3, StringFormat=F2}" 
                                     materialDesign:HintAssist.Hint="Al2O3目标值(%)"
                                     Style="{StaticResource NumericTextBoxStyle}" 
                                     Margin="8,0,0,8"/>
                        </Grid>
                    </GroupBox>

                    <!-- 目标值说明 -->
                    <GroupBox Header="参数说明" Style="{StaticResource MaterialGroupBoxStyle}">
                        <StackPanel>
                            <TextBlock Text="• TFe目标值：烧结矿中铁含量目标，通常在50-70%之间" Style="{StaticResource BodyTextStyle}" Margin="0,2"/>
                            <TextBlock Text="• 碱度目标值：CaO/SiO2比值，通常在1.0-3.0之间" Style="{StaticResource BodyTextStyle}" Margin="0,2"/>
                            <TextBlock Text="• MgO目标值：氧化镁含量，通常在1.0-5.0%之间" Style="{StaticResource BodyTextStyle}" Margin="0,2"/>
                            <TextBlock Text="• Al2O3目标值：氧化铝含量，通常在1.0-3.0%之间" Style="{StaticResource BodyTextStyle}" Margin="0,2"/>
                        </StackPanel>
                    </GroupBox>

                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- 操作按钮栏 -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}" Margin="0,8,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="验证配置" Command="{Binding ValidateConfigurationCommand}" 
                        Style="{StaticResource MaterialDesignOutlinedButton}" Margin="4"/>
                <Button Content="重置默认" Command="{Binding ResetToDefaultCommand}" 
                        Style="{StaticResource MaterialDesignOutlinedButton}" Margin="4"/>
                <Button Content="保存配置" Command="{Binding SaveConfigurationCommand}" 
                        Style="{StaticResource MaterialDesignRaisedAccentButton}" Margin="4"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
