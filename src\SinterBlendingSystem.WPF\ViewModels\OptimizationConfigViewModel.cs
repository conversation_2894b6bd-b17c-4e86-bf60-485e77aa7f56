using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using SinterBlendingSystem.Core.Interfaces;
using SinterBlendingSystem.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SinterBlendingSystem.WPF.ViewModels
{
    /// <summary>
    /// 优化配置ViewModel
    /// </summary>
    public class OptimizationConfigViewModel : ViewModelBase
    {
        private readonly IDataService _dataService;
        private OptimizationTarget _target = new();
        private ConstraintRanges _constraints = new();

        public OptimizationConfigViewModel(ILogger logger, IDataService dataService) : base(logger)
        {
            _dataService = dataService;
            InitializeCommands();
        }

        #region 属性

        /// <summary>
        /// 优化目标
        /// </summary>
        public OptimizationTarget Target
        {
            get => _target;
            set => SetProperty(ref _target, value);
        }

        /// <summary>
        /// 约束条件
        /// </summary>
        public ConstraintRanges Constraints
        {
            get => _constraints;
            set => SetProperty(ref _constraints, value);
        }

        #endregion

        #region 命令

        public IAsyncRelayCommand LoadConfigurationCommand { get; private set; } = null!;
        public IAsyncRelayCommand SaveConfigurationCommand { get; private set; } = null!;
        public IAsyncRelayCommand ResetToDefaultCommand { get; private set; } = null!;
        public IRelayCommand ValidateConfigurationCommand { get; private set; } = null!;

        #endregion

        #region 初始化

        private void InitializeCommands()
        {
            LoadConfigurationCommand = new AsyncRelayCommand(LoadConfigurationAsync);
            SaveConfigurationCommand = new AsyncRelayCommand(SaveConfigurationAsync);
            ResetToDefaultCommand = new AsyncRelayCommand(ResetToDefaultAsync);
            ValidateConfigurationCommand = new RelayCommand(ValidateConfiguration);
        }

        public override async Task InitializeAsync()
        {
            await base.InitializeAsync();
            await LoadConfigurationAsync();
        }

        #endregion

        #region 配置操作

        public async Task LoadConfigurationAsync()
        {
            await ExecuteAsync(async () =>
            {
                Target = await _dataService.GetOptimizationTargetAsync();
                Constraints = await _dataService.GetConstraintRangesAsync();

                _logger.LogInformation("加载优化配置成功");
            }, "正在加载配置...", "加载配置失败");
        }

        public async Task SaveConfigurationAsync()
        {
            await ExecuteAsync(async () =>
            {
                // 验证配置
                var targetValidation = Target.Validate();
                var constraintValidation = Constraints.Validate();

                if (!targetValidation.IsValid)
                {
                    throw new InvalidOperationException($"目标配置无效：{string.Join(", ", targetValidation.Errors)}");
                }

                if (!constraintValidation.IsValid)
                {
                    throw new InvalidOperationException($"约束配置无效：{string.Join(", ", constraintValidation.Errors)}");
                }

                // 保存配置
                var targetSaved = await _dataService.SaveOptimizationTargetAsync(Target);
                var constraintsSaved = await _dataService.SaveConstraintRangesAsync(Constraints);

                if (!targetSaved || !constraintsSaved)
                {
                    throw new InvalidOperationException("保存配置失败");
                }

                _logger.LogInformation("保存优化配置成功");
            }, "正在保存配置...", "保存配置失败");
        }

        private async Task ResetToDefaultAsync()
        {
            await ExecuteAsync(async () =>
            {
                Target = new OptimizationTarget();
                Constraints = new ConstraintRanges();

                _logger.LogInformation("重置为默认配置");
                await Task.CompletedTask;
            }, "正在重置配置...", "重置配置失败");
        }

        private void ValidateConfiguration()
        {
            try
            {
                ClearError();

                var targetValidation = Target.Validate();
                var constraintValidation = Constraints.Validate();

                var errors = new List<string>();
                if (!targetValidation.IsValid)
                {
                    errors.AddRange(targetValidation.Errors);
                }

                if (!constraintValidation.IsValid)
                {
                    errors.AddRange(constraintValidation.Errors);
                }

                if (errors.Any())
                {
                    ErrorMessage = $"配置验证失败：{string.Join(", ", errors)}";
                }
                else
                {
                    // 可以显示验证成功的消息
                    _logger.LogInformation("配置验证通过");
                }
            }
            catch (Exception ex)
            {
                HandleException(ex, "配置验证失败");
            }
        }

        #endregion
    }
}
