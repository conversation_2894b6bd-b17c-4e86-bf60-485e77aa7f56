using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SinterBlendingSystem.Core.Interfaces;
using SinterBlendingSystem.Core.Services;
using SinterBlendingSystem.WPF.ViewModels;
using System;

namespace TestDI
{
    class Program
    {
        static void Main(string[] args)
        {
            try
            {
                Console.WriteLine("测试依赖注入配置...");
                
                var host = Host.CreateDefaultBuilder()
                    .ConfigureServices((context, services) =>
                    {
                        // 注册服务
                        services.AddHttpClient();
                        services.AddLogging(builder =>
                        {
                            builder.AddConsole();
                            builder.AddDebug();
                            builder.SetMinimumLevel(LogLevel.Information);
                        });

                        // 注册核心服务
                        services.AddSingleton<IDataService, DataService>();
                        services.AddSingleton<IOptimizationService, OptimizationService>();

                        // 注册ViewModels
                        services.AddTransient<MainWindowViewModel>();
                        services.AddTransient<MaterialListViewModel>();
                        services.AddTransient<OptimizationConfigViewModel>();
                        services.AddTransient<ResultDisplayViewModel>();
                    })
                    .Build();

                Console.WriteLine("✓ 依赖注入容器创建成功");

                // 测试服务解析
                var dataService = host.Services.GetRequiredService<IDataService>();
                Console.WriteLine("✓ DataService 解析成功");

                var optimizationService = host.Services.GetRequiredService<IOptimizationService>();
                Console.WriteLine("✓ OptimizationService 解析成功");

                var mainWindowViewModel = host.Services.GetRequiredService<MainWindowViewModel>();
                Console.WriteLine("✓ MainWindowViewModel 解析成功");

                Console.WriteLine("所有依赖注入测试通过！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 依赖注入测试失败: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
        }
    }
}
