using System.ComponentModel;

namespace SinterBlendingSystem.Core.Models
{
    /// <summary>
    /// 优化结果模型
    /// </summary>
    public class OptimizationResult : INotifyPropertyChanged
    {
        private bool _success;
        private string _message = string.Empty;
        private OptimizationType _optimizationType;
        private double _objectiveValue;
        private int _iterations;
        private DateTime _timestamp = DateTime.Now;
        private Dictionary<string, double> _optimalRatios = new();
        private SinterProperties _sinterProperties = new();
        private double _unitCost;
        private List<string> _warnings = new();

        /// <summary>
        /// 优化是否成功
        /// </summary>
        public bool Success
        {
            get => _success;
            set
            {
                if (_success != value)
                {
                    _success = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message
        {
            get => _message;
            set
            {
                if (_message != value)
                {
                    _message = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 优化类型
        /// </summary>
        public OptimizationType OptimizationType
        {
            get => _optimizationType;
            set
            {
                if (_optimizationType != value)
                {
                    _optimizationType = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 目标函数值
        /// </summary>
        public double ObjectiveValue
        {
            get => _objectiveValue;
            set
            {
                if (Math.Abs(_objectiveValue - value) > 0.001)
                {
                    _objectiveValue = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 迭代次数
        /// </summary>
        public int Iterations
        {
            get => _iterations;
            set
            {
                if (_iterations != value)
                {
                    _iterations = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 计算时间戳
        /// </summary>
        public DateTime Timestamp
        {
            get => _timestamp;
            set
            {
                if (_timestamp != value)
                {
                    _timestamp = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 最优配比结果（原料名称 -> 配比%）
        /// </summary>
        public Dictionary<string, double> OptimalRatios
        {
            get => _optimalRatios;
            set
            {
                if (_optimalRatios != value)
                {
                    _optimalRatios = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 烧结矿性质
        /// </summary>
        public SinterProperties SinterProperties
        {
            get => _sinterProperties;
            set
            {
                if (_sinterProperties != value)
                {
                    _sinterProperties = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 单位成本(元/吨)
        /// </summary>
        public double UnitCost
        {
            get => _unitCost;
            set
            {
                if (Math.Abs(_unitCost - value) > 0.001)
                {
                    _unitCost = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 警告信息列表
        /// </summary>
        public List<string> Warnings
        {
            get => _warnings;
            set
            {
                if (_warnings != value)
                {
                    _warnings = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 计算耗时(毫秒)
        /// </summary>
        public double CalculationTime { get; set; }

        /// <summary>
        /// 是否有警告
        /// </summary>
        public bool HasWarnings => Warnings.Count > 0;

        /// <summary>
        /// 配比总和
        /// </summary>
        public double TotalRatio => OptimalRatios.Values.Sum();

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 添加警告信息
        /// </summary>
        /// <param name="warning">警告信息</param>
        public void AddWarning(string warning)
        {
            if (!Warnings.Contains(warning))
            {
                Warnings.Add(warning);
                OnPropertyChanged(nameof(Warnings));
                OnPropertyChanged(nameof(HasWarnings));
            }
        }

        /// <summary>
        /// 清除警告信息
        /// </summary>
        public void ClearWarnings()
        {
            if (Warnings.Count > 0)
            {
                Warnings.Clear();
                OnPropertyChanged(nameof(Warnings));
                OnPropertyChanged(nameof(HasWarnings));
            }
        }

        /// <summary>
        /// 验证结果的合理性
        /// </summary>
        /// <param name="constraints">约束条件</param>
        /// <returns>验证结果</returns>
        public ValidationResult ValidateResult(ConstraintRanges constraints)
        {
            var errors = new List<string>();

            if (!Success)
            {
                errors.Add("优化失败");
                return new ValidationResult { IsValid = false, Errors = errors };
            }

            // 检查配比总和
            var totalRatio = TotalRatio;
            if (Math.Abs(totalRatio - 100.0) > 0.1)
            {
                errors.Add($"配比总和不等于100%，当前为{totalRatio:F2}%");
            }

            // 检查烧结矿性质是否在约束范围内
            if (!constraints.IsInRange(ComponentType.TFe, SinterProperties.TFe))
            {
                errors.Add($"TFe含量{SinterProperties.TFe:F2}%超出约束范围{constraints.TFeRange}");
            }

            if (!constraints.IsInRange(ComponentType.R, SinterProperties.R))
            {
                errors.Add($"碱度{SinterProperties.R:F2}超出约束范围{constraints.RRange}");
            }

            if (!constraints.IsInRange(ComponentType.MgO, SinterProperties.MgO))
            {
                errors.Add($"MgO含量{SinterProperties.MgO:F2}%超出约束范围{constraints.MgORange}");
            }

            if (!constraints.IsInRange(ComponentType.Al2O3, SinterProperties.Al2O3))
            {
                errors.Add($"Al2O3含量{SinterProperties.Al2O3:F2}%超出约束范围{constraints.Al2O3Range}");
            }

            if (!constraints.IsInRange(ComponentType.Cost, UnitCost))
            {
                errors.Add($"单位成本{UnitCost:F2}元/吨超出约束范围{constraints.CostRange}");
            }

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors
            };
        }

        /// <summary>
        /// 克隆结果对象
        /// </summary>
        /// <returns>克隆的结果对象</returns>
        public OptimizationResult Clone()
        {
            return new OptimizationResult
            {
                Success = this.Success,
                Message = this.Message,
                OptimizationType = this.OptimizationType,
                ObjectiveValue = this.ObjectiveValue,
                Iterations = this.Iterations,
                Timestamp = this.Timestamp,
                OptimalRatios = new Dictionary<string, double>(this.OptimalRatios),
                SinterProperties = this.SinterProperties.Clone(),
                UnitCost = this.UnitCost,
                Warnings = new List<string>(this.Warnings),
                CalculationTime = this.CalculationTime
            };
        }
    }

    /// <summary>
    /// 烧结矿性质
    /// </summary>
    public class SinterProperties : INotifyPropertyChanged
    {
        private double _tfe;
        private double _r;
        private double _mgo;
        private double _al2o3;
        private double _sio2;
        private double _cao;

        /// <summary>
        /// TFe含量(%)
        /// </summary>
        public double TFe
        {
            get => _tfe;
            set
            {
                if (Math.Abs(_tfe - value) > 0.001)
                {
                    _tfe = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 碱度(CaO/SiO2)
        /// </summary>
        public double R
        {
            get => _r;
            set
            {
                if (Math.Abs(_r - value) > 0.001)
                {
                    _r = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// MgO含量(%)
        /// </summary>
        public double MgO
        {
            get => _mgo;
            set
            {
                if (Math.Abs(_mgo - value) > 0.001)
                {
                    _mgo = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Al2O3含量(%)
        /// </summary>
        public double Al2O3
        {
            get => _al2o3;
            set
            {
                if (Math.Abs(_al2o3 - value) > 0.001)
                {
                    _al2o3 = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// SiO2含量(%)
        /// </summary>
        public double SiO2
        {
            get => _sio2;
            set
            {
                if (Math.Abs(_sio2 - value) > 0.001)
                {
                    _sio2 = value;
                    OnPropertyChanged();
                    // SiO2变化会影响碱度计算
                    OnPropertyChanged(nameof(R));
                }
            }
        }

        /// <summary>
        /// CaO含量(%)
        /// </summary>
        public double CaO
        {
            get => _cao;
            set
            {
                if (Math.Abs(_cao - value) > 0.001)
                {
                    _cao = value;
                    OnPropertyChanged();
                    // CaO变化会影响碱度计算
                    OnPropertyChanged(nameof(R));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 克隆性质对象
        /// </summary>
        /// <returns>克隆的性质对象</returns>
        public SinterProperties Clone()
        {
            return new SinterProperties
            {
                TFe = this.TFe,
                R = this.R,
                MgO = this.MgO,
                Al2O3 = this.Al2O3,
                SiO2 = this.SiO2,
                CaO = this.CaO
            };
        }
    }
}
