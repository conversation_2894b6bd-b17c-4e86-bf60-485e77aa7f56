using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SinterBlendingSystem.Core.Interfaces;
using SinterBlendingSystem.Core.Services;
using SinterBlendingSystem.WPF.ViewModels;
using Xunit;

namespace SinterBlendingSystem.Tests
{
    public class StartupTest
    {
        [Fact]
        public void CanCreateServiceProvider()
        {
            // 测试依赖注入容器是否能正常创建
            var host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // 注册服务
                    services.AddHttpClient();
                    services.AddLogging(builder =>
                    {
                        builder.AddConsole();
                        builder.SetMinimumLevel(LogLevel.Information);
                    });

                    // 注册核心服务
                    services.AddSingleton<IDataService, DataService>();
                    services.AddSingleton<IOptimizationService, OptimizationService>();

                    // 注册ViewModels
                    services.AddTransient<MainWindowViewModel>();
                    services.AddTransient<MaterialListViewModel>();
                    services.AddTransient<OptimizationConfigViewModel>();
                    services.AddTransient<ResultDisplayViewModel>();
                })
                .Build();

            // 验证服务可以正常创建
            var dataService = host.Services.GetRequiredService<IDataService>();
            var optimizationService = host.Services.GetRequiredService<IOptimizationService>();
            var mainViewModel = host.Services.GetRequiredService<MainWindowViewModel>();

            Assert.NotNull(dataService);
            Assert.NotNull(optimizationService);
            Assert.NotNull(mainViewModel);
        }

        [Fact]
        public async Task DataServiceCanLoadDefaultMaterials()
        {
            // 测试数据服务是否能正常工作
            var host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    services.AddLogging(builder => builder.AddConsole());
                    services.AddSingleton<IDataService, DataService>();
                })
                .Build();

            var dataService = host.Services.GetRequiredService<IDataService>();
            var materials = await dataService.GetMaterialsAsync();

            Assert.NotNull(materials);
            Assert.True(materials.Any());
        }
    }
}
