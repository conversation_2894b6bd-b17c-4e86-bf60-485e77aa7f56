using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace SinterBlendingSystem.Core.Models
{
    /// <summary>
    /// 原料数据模型
    /// </summary>
    public class MaterialData : INotifyPropertyChanged
    {
        private string _code = string.Empty;
        private string _name = string.Empty;
        private double _tfeWet;
        private double _sio2Wet;
        private double _caoWet;
        private double _mgoWet;
        private double _al2o3Wet;
        private double _h2o;
        private double _ig;
        private double _price;
        private double _minRatio;
        private double _maxRatio;
        private double _currentRatio;
        private bool _isLocked;
        private MaterialStatus _status = MaterialStatus.Normal;

        /// <summary>
        /// 原料代码
        /// </summary>
        [Required]
        [StringLength(6, ErrorMessage = "代码长度不能超过6个字符")]
        public string Code
        {
            get => _code;
            set
            {
                if (_code != value)
                {
                    _code = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 原料名称
        /// </summary>
        [Required]
        [StringLength(50, ErrorMessage = "名称长度不能超过50个字符")]
        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// TFe含量(湿基，%)
        /// </summary>
        [Range(0, 100, ErrorMessage = "TFe含量必须在0-100%之间")]
        public double TFeWet
        {
            get => _tfeWet;
            set
            {
                if (Math.Abs(_tfeWet - value) > 0.001)
                {
                    _tfeWet = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TFeDry));
                    UpdateStatus();
                }
            }
        }

        /// <summary>
        /// SiO2含量(湿基，%)
        /// </summary>
        [Range(0, 100, ErrorMessage = "SiO2含量必须在0-100%之间")]
        public double SiO2Wet
        {
            get => _sio2Wet;
            set
            {
                if (Math.Abs(_sio2Wet - value) > 0.001)
                {
                    _sio2Wet = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(SiO2Dry));
                    UpdateStatus();
                }
            }
        }

        /// <summary>
        /// CaO含量(湿基，%)
        /// </summary>
        [Range(0, 100, ErrorMessage = "CaO含量必须在0-100%之间")]
        public double CaOWet
        {
            get => _caoWet;
            set
            {
                if (Math.Abs(_caoWet - value) > 0.001)
                {
                    _caoWet = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(CaODry));
                    UpdateStatus();
                }
            }
        }

        /// <summary>
        /// MgO含量(湿基，%)
        /// </summary>
        [Range(0, 100, ErrorMessage = "MgO含量必须在0-100%之间")]
        public double MgOWet
        {
            get => _mgoWet;
            set
            {
                if (Math.Abs(_mgoWet - value) > 0.001)
                {
                    _mgoWet = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(MgODry));
                }
            }
        }

        /// <summary>
        /// Al2O3含量(湿基，%)
        /// </summary>
        [Range(0, 100, ErrorMessage = "Al2O3含量必须在0-100%之间")]
        public double Al2O3Wet
        {
            get => _al2o3Wet;
            set
            {
                if (Math.Abs(_al2o3Wet - value) > 0.001)
                {
                    _al2o3Wet = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(Al2O3Dry));
                }
            }
        }

        /// <summary>
        /// 水分(%)
        /// </summary>
        [Range(0, 20, ErrorMessage = "水分必须在0-20%之间")]
        public double H2O
        {
            get => _h2o;
            set
            {
                if (Math.Abs(_h2o - value) > 0.001)
                {
                    _h2o = value;
                    OnPropertyChanged();
                    // 水分变化会影响所有干基成分
                    OnPropertyChanged(nameof(TFeDry));
                    OnPropertyChanged(nameof(SiO2Dry));
                    OnPropertyChanged(nameof(CaODry));
                    OnPropertyChanged(nameof(MgODry));
                    OnPropertyChanged(nameof(Al2O3Dry));
                    UpdateStatus();
                }
            }
        }

        /// <summary>
        /// 烧损(%)
        /// </summary>
        [Range(-5, 80, ErrorMessage = "烧损必须在-5-80%之间")]
        public double Ig
        {
            get => _ig;
            set
            {
                if (Math.Abs(_ig - value) > 0.001)
                {
                    _ig = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 价格(元/吨)
        /// </summary>
        [Range(0, 10000, ErrorMessage = "价格必须在0-10000元/吨之间")]
        public double Price
        {
            get => _price;
            set
            {
                if (Math.Abs(_price - value) > 0.001)
                {
                    _price = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 最小配比(%)
        /// </summary>
        [Range(0, 100, ErrorMessage = "最小配比必须在0-100%之间")]
        public double MinRatio
        {
            get => _minRatio;
            set
            {
                if (Math.Abs(_minRatio - value) > 0.001)
                {
                    _minRatio = value;
                    OnPropertyChanged();
                    ValidateRatioRange();
                }
            }
        }

        /// <summary>
        /// 最大配比(%)
        /// </summary>
        [Range(0, 100, ErrorMessage = "最大配比必须在0-100%之间")]
        public double MaxRatio
        {
            get => _maxRatio;
            set
            {
                if (Math.Abs(_maxRatio - value) > 0.001)
                {
                    _maxRatio = value;
                    OnPropertyChanged();
                    ValidateRatioRange();
                }
            }
        }

        /// <summary>
        /// 当前配比(%)
        /// </summary>
        [Range(0, 100, ErrorMessage = "当前配比必须在0-100%之间")]
        public double CurrentRatio
        {
            get => _currentRatio;
            set
            {
                if (Math.Abs(_currentRatio - value) > 0.001)
                {
                    _currentRatio = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 是否锁定（系统内置原料不可修改）
        /// </summary>
        public bool IsLocked
        {
            get => _isLocked;
            set
            {
                if (_isLocked != value)
                {
                    _isLocked = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 原料状态
        /// </summary>
        public MaterialStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                }
            }
        }

        // 计算属性：干基成分
        /// <summary>
        /// TFe含量(干基，%)
        /// </summary>
        public double TFeDry => H2O >= 100 ? 0 : TFeWet / (1 - H2O / 100.0);

        /// <summary>
        /// SiO2含量(干基，%)
        /// </summary>
        public double SiO2Dry => H2O >= 100 ? 0 : SiO2Wet / (1 - H2O / 100.0);

        /// <summary>
        /// CaO含量(干基，%)
        /// </summary>
        public double CaODry => H2O >= 100 ? 0 : CaOWet / (1 - H2O / 100.0);

        /// <summary>
        /// MgO含量(干基，%)
        /// </summary>
        public double MgODry => H2O >= 100 ? 0 : MgOWet / (1 - H2O / 100.0);

        /// <summary>
        /// Al2O3含量(干基，%)
        /// </summary>
        public double Al2O3Dry => H2O >= 100 ? 0 : Al2O3Wet / (1 - H2O / 100.0);

        /// <summary>
        /// 原料类型（根据TFe含量判断）
        /// </summary>
        public MaterialType Type
        {
            get
            {
                if (TFeWet > 50) return MaterialType.IronOre;
                if (CaOWet > 20) return MaterialType.Flux;
                if (Ig > 50) return MaterialType.Fuel;
                return MaterialType.Other;
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void UpdateStatus()
        {
            // 根据成分范围更新状态
            var errorCount = 0;
            var warningCount = 0;

            // TFe检查（铁矿类原料）
            if (Type == MaterialType.IronOre)
            {
                if (TFeWet < 55 || TFeWet > 65)
                {
                    if (TFeWet < 50 || TFeWet > 70) errorCount++;
                    else warningCount++;
                }
            }

            // SiO2检查
            if (SiO2Wet < 4 || SiO2Wet > 6)
            {
                if (SiO2Wet < 2 || SiO2Wet > 8) errorCount++;
                else warningCount++;
            }

            // 水分检查
            if (H2O > 20) errorCount++;
            else if (H2O > 15) warningCount++;

            // 更新状态
            if (errorCount > 0)
                Status = MaterialStatus.Error;
            else if (warningCount > 0)
                Status = MaterialStatus.Warning;
            else
                Status = MaterialStatus.Normal;
        }

        private void ValidateRatioRange()
        {
            // 只在MaxRatio已经设置且大于0时才验证
            if (MaxRatio > 0 && MinRatio > MaxRatio)
            {
                throw new ArgumentException("最小配比不能大于最大配比");
            }
        }
    }

    /// <summary>
    /// 原料状态枚举
    /// </summary>
    public enum MaterialStatus
    {
        Normal,     // 正常
        Warning,    // 警告
        Error,      // 错误
        Editing,    // 编辑中
        Locked      // 锁定
    }

    /// <summary>
    /// 原料类型枚举
    /// </summary>
    public enum MaterialType
    {
        IronOre,    // 铁矿
        Flux,       // 熔剂
        Fuel,       // 燃料
        Other       // 其他
    }
}
