using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using SinterBlendingSystem.Core.Interfaces;
using SinterBlendingSystem.Core.Models;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace SinterBlendingSystem.WPF.ViewModels
{
    /// <summary>
    /// 原料列表ViewModel
    /// </summary>
    public class MaterialListViewModel : ViewModelBase
    {
        private readonly IDataService _dataService;
        private ObservableCollection<MaterialData> _materials = new();
        private MaterialData? _selectedMaterial;
        private string _searchText = string.Empty;
        private bool _showLockedMaterials = true;

        public MaterialListViewModel(ILogger logger, IDataService dataService) : base(logger)
        {
            _dataService = dataService;
            InitializeCommands();
        }

        #region 属性

        /// <summary>
        /// 原料列表
        /// </summary>
        public ObservableCollection<MaterialData> Materials
        {
            get => _materials;
            set => SetProperty(ref _materials, value);
        }

        /// <summary>
        /// 选中的原料
        /// </summary>
        public MaterialData? SelectedMaterial
        {
            get => _selectedMaterial;
            set
            {
                SetProperty(ref _selectedMaterial, value);
                OnPropertyChanged(nameof(HasSelectedMaterial));
                // 更新命令状态
                EditMaterialCommand.NotifyCanExecuteChanged();
                DeleteMaterialCommand.NotifyCanExecuteChanged();
                CopyMaterialCommand.NotifyCanExecuteChanged();
            }
        }

        /// <summary>
        /// 是否有选中的原料
        /// </summary>
        public bool HasSelectedMaterial => SelectedMaterial != null;

        /// <summary>
        /// 搜索文本
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set
            {
                SetProperty(ref _searchText, value);
                _ = FilterMaterialsAsync();
            }
        }

        /// <summary>
        /// 是否显示锁定的原料
        /// </summary>
        public bool ShowLockedMaterials
        {
            get => _showLockedMaterials;
            set
            {
                SetProperty(ref _showLockedMaterials, value);
                _ = FilterMaterialsAsync();
            }
        }

        #endregion

        #region 命令

        public IAsyncRelayCommand LoadMaterialsCommand { get; private set; } = null!;
        public IAsyncRelayCommand AddMaterialCommand { get; private set; } = null!;
        public IAsyncRelayCommand EditMaterialCommand { get; private set; } = null!;
        public IAsyncRelayCommand DeleteMaterialCommand { get; private set; } = null!;
        public IAsyncRelayCommand CopyMaterialCommand { get; private set; } = null!;
        public IAsyncRelayCommand SaveMaterialsCommand { get; private set; } = null!;
        public IAsyncRelayCommand ResetToDefaultCommand { get; private set; } = null!;
        public IRelayCommand ClearSearchCommand { get; private set; } = null!;

        #endregion

        #region 初始化

        private void InitializeCommands()
        {
            LoadMaterialsCommand = new AsyncRelayCommand(LoadMaterialsAsync);
            AddMaterialCommand = new AsyncRelayCommand(AddMaterialAsync);
            EditMaterialCommand = new AsyncRelayCommand(EditMaterialAsync, CanEditMaterial);
            DeleteMaterialCommand = new AsyncRelayCommand(DeleteMaterialAsync, CanDeleteMaterial);
            CopyMaterialCommand = new AsyncRelayCommand(CopyMaterialAsync, CanCopyMaterial);
            SaveMaterialsCommand = new AsyncRelayCommand(SaveMaterialsAsync);
            ResetToDefaultCommand = new AsyncRelayCommand(ResetToDefaultAsync);
            ClearSearchCommand = new RelayCommand(ClearSearch);
        }

        public override async Task InitializeAsync()
        {
            await base.InitializeAsync();
            await LoadMaterialsAsync();
        }

        #endregion

        #region 数据操作

        private async Task LoadMaterialsAsync()
        {
            await ExecuteAsync(async () =>
            {
                var materials = await _dataService.GetMaterialsAsync();
                Materials.Clear();
                
                foreach (var material in materials)
                {
                    Materials.Add(material);
                }

                _logger.LogInformation("已加载{Count}条原料数据", Materials.Count);
            }, "正在加载原料数据...", "加载原料数据失败");
        }

        private async Task AddMaterialAsync()
        {
            await ExecuteAsync(async () =>
            {
                var newMaterial = new MaterialData
                {
                    Code = GenerateNewMaterialCode(),
                    Name = "新原料",
                    Status = MaterialStatus.Editing
                };

                Materials.Add(newMaterial);
                SelectedMaterial = newMaterial;

                _logger.LogInformation("添加新原料：{Code}", newMaterial.Code);
            }, "正在添加原料...", "添加原料失败");
        }

        private async Task EditMaterialAsync()
        {
            if (SelectedMaterial == null) return;

            await ExecuteAsync(async () =>
            {
                // 这里可以打开编辑对话框或切换到编辑模式
                SelectedMaterial.Status = MaterialStatus.Editing;
                
                _logger.LogInformation("开始编辑原料：{Code}", SelectedMaterial.Code);
                await Task.CompletedTask;
            }, "正在编辑原料...", "编辑原料失败");
        }

        private async Task DeleteMaterialAsync()
        {
            if (SelectedMaterial == null) return;

            var result = MessageBox.Show(
                $"确定要删除原料 '{SelectedMaterial.Name}' 吗？",
                "确认删除",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result != MessageBoxResult.Yes) return;

            await ExecuteAsync(async () =>
            {
                var success = await _dataService.DeleteMaterialAsync(SelectedMaterial.Code);
                if (success)
                {
                    Materials.Remove(SelectedMaterial);
                    SelectedMaterial = null;
                    _logger.LogInformation("删除原料成功");
                }
                else
                {
                    throw new InvalidOperationException("删除原料失败");
                }
            }, "正在删除原料...", "删除原料失败");
        }

        private async Task CopyMaterialAsync()
        {
            if (SelectedMaterial == null) return;

            await ExecuteAsync(async () =>
            {
                var copiedMaterial = new MaterialData
                {
                    Code = GenerateNewMaterialCode(),
                    Name = $"{SelectedMaterial.Name}_副本",
                    TFeWet = SelectedMaterial.TFeWet,
                    SiO2Wet = SelectedMaterial.SiO2Wet,
                    CaOWet = SelectedMaterial.CaOWet,
                    MgOWet = SelectedMaterial.MgOWet,
                    Al2O3Wet = SelectedMaterial.Al2O3Wet,
                    H2O = SelectedMaterial.H2O,
                    Ig = SelectedMaterial.Ig,
                    Price = SelectedMaterial.Price,
                    MinRatio = SelectedMaterial.MinRatio,
                    MaxRatio = SelectedMaterial.MaxRatio,
                    Status = MaterialStatus.Editing
                };

                Materials.Add(copiedMaterial);
                SelectedMaterial = copiedMaterial;

                _logger.LogInformation("复制原料：{Code}", copiedMaterial.Code);
                await Task.CompletedTask;
            }, "正在复制原料...", "复制原料失败");
        }

        private async Task SaveMaterialsAsync()
        {
            await ExecuteAsync(async () =>
            {
                var success = await _dataService.SaveMaterialsAsync(Materials);
                if (success)
                {
                    // 更新所有原料状态为正常
                    foreach (var material in Materials)
                    {
                        if (material.Status == MaterialStatus.Editing)
                        {
                            material.Status = MaterialStatus.Normal;
                        }
                    }
                    
                    _logger.LogInformation("保存原料数据成功");
                }
                else
                {
                    throw new InvalidOperationException("保存原料数据失败");
                }
            }, "正在保存原料数据...", "保存原料数据失败");
        }

        private async Task ResetToDefaultAsync()
        {
            var result = MessageBox.Show(
                "确定要重置为默认原料数据吗？这将清除所有自定义原料。",
                "确认重置",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result != MessageBoxResult.Yes) return;

            await ExecuteAsync(async () =>
            {
                var success = await _dataService.ResetToDefaultDataAsync();
                if (success)
                {
                    await LoadMaterialsAsync();
                    _logger.LogInformation("重置为默认数据成功");
                }
                else
                {
                    throw new InvalidOperationException("重置为默认数据失败");
                }
            }, "正在重置数据...", "重置数据失败");
        }

        #endregion

        #region 搜索和过滤

        private async Task FilterMaterialsAsync()
        {
            await Task.Run(() =>
            {
                // 这里可以实现更复杂的过滤逻辑
                // 暂时只是简单的文本搜索
                Application.Current.Dispatcher.Invoke(() =>
                {
                    // 实际的过滤逻辑可以在这里实现
                    // 例如使用CollectionView进行过滤
                });
            });
        }

        private void ClearSearch()
        {
            SearchText = string.Empty;
        }

        #endregion

        #region 辅助方法

        private string GenerateNewMaterialCode()
        {
            var existingCodes = Materials.Select(m => m.Code).ToHashSet();
            var baseCode = "M";
            var counter = 1;

            string newCode;
            do
            {
                newCode = $"{baseCode}{counter:D3}";
                counter++;
            } while (existingCodes.Contains(newCode));

            return newCode;
        }

        private bool CanEditMaterial()
        {
            return SelectedMaterial != null && !SelectedMaterial.IsLocked;
        }

        private bool CanDeleteMaterial()
        {
            return SelectedMaterial != null && !SelectedMaterial.IsLocked;
        }

        private bool CanCopyMaterial()
        {
            return SelectedMaterial != null;
        }

        #endregion
    }
}
