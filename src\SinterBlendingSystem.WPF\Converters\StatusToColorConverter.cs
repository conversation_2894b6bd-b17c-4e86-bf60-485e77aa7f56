using SinterBlendingSystem.Core.Models;
using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace SinterBlendingSystem.WPF.Converters
{
    /// <summary>
    /// 状态到颜色转换器
    /// </summary>
    public class StatusToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is MaterialStatus status)
            {
                return status switch
                {
                    MaterialStatus.Normal => Colors.Green,
                    MaterialStatus.Warning => Colors.Orange,
                    MaterialStatus.Error => Colors.Red,
                    MaterialStatus.Editing => Colors.Blue,
                    MaterialStatus.Locked => Colors.Gray,
                    _ => Colors.Gray
                };
            }

            return Colors.Gray;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 状态到画刷转换器
    /// </summary>
    public class StatusToBrushConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is MaterialStatus status)
            {
                var color = status switch
                {
                    MaterialStatus.Normal => Colors.Green,
                    MaterialStatus.Warning => Colors.Orange,
                    MaterialStatus.Error => Colors.Red,
                    MaterialStatus.Editing => Colors.Blue,
                    MaterialStatus.Locked => Colors.Gray,
                    _ => Colors.Gray
                };

                return new SolidColorBrush(color);
            }

            return new SolidColorBrush(Colors.Gray);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 枚举到布尔转换器
    /// </summary>
    public class EnumToBooleanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null || parameter == null)
                return false;

            return value.ToString() == parameter.ToString();
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue && boolValue && parameter != null)
            {
                return Enum.Parse(targetType, parameter.ToString()!);
            }

            return Binding.DoNothing;
        }
    }
}
