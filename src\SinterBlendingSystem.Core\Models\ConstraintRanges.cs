using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace SinterBlendingSystem.Core.Models
{
    /// <summary>
    /// 约束范围模型
    /// </summary>
    public class ConstraintRanges : INotifyPropertyChanged
    {
        private ComponentRange _tfeRange = new ComponentRange(54, 56);
        private ComponentRange _rRange = new ComponentRange(1.75, 2.05);
        private ComponentRange _mgoRange = new ComponentRange(1.8, 3.0);
        private ComponentRange _al2o3Range = new ComponentRange(1.5, 2.5);
        private ComponentRange _costRange = new ComponentRange(600, 680);

        /// <summary>
        /// TFe含量约束范围(%)
        /// </summary>
        public ComponentRange TFeRange
        {
            get => _tfeRange;
            set
            {
                if (_tfeRange != value)
                {
                    _tfeRange = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 碱度约束范围
        /// </summary>
        public ComponentRange RRange
        {
            get => _rRange;
            set
            {
                if (_rRange != value)
                {
                    _rRange = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// MgO含量约束范围(%)
        /// </summary>
        public ComponentRange MgORange
        {
            get => _mgoRange;
            set
            {
                if (_mgoRange != value)
                {
                    _mgoRange = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Al2O3含量约束范围(%)
        /// </summary>
        public ComponentRange Al2O3Range
        {
            get => _al2o3Range;
            set
            {
                if (_al2o3Range != value)
                {
                    _al2o3Range = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 成本约束范围(元/吨)
        /// </summary>
        public ComponentRange CostRange
        {
            get => _costRange;
            set
            {
                if (_costRange != value)
                {
                    _costRange = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 验证约束范围的合理性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var errors = new List<string>();

            // 验证TFe范围
            if (!TFeRange.IsValid())
                errors.Add("TFe约束范围无效：最小值不能大于最大值");
            if (TFeRange.Min < 40 || TFeRange.Max > 80)
                errors.Add("TFe约束范围超出合理范围(40-80%)");

            // 验证碱度范围
            if (!RRange.IsValid())
                errors.Add("碱度约束范围无效：最小值不能大于最大值");
            if (RRange.Min < 0.5 || RRange.Max > 4.0)
                errors.Add("碱度约束范围超出合理范围(0.5-4.0)");

            // 验证MgO范围
            if (!MgORange.IsValid())
                errors.Add("MgO约束范围无效：最小值不能大于最大值");
            if (MgORange.Min < 0.5 || MgORange.Max > 6.0)
                errors.Add("MgO约束范围超出合理范围(0.5-6.0%)");

            // 验证Al2O3范围
            if (!Al2O3Range.IsValid())
                errors.Add("Al2O3约束范围无效：最小值不能大于最大值");
            if (Al2O3Range.Min < 0.5 || Al2O3Range.Max > 4.0)
                errors.Add("Al2O3约束范围超出合理范围(0.5-4.0%)");

            // 验证成本范围
            if (!CostRange.IsValid())
                errors.Add("成本约束范围无效：最小值不能大于最大值");
            if (CostRange.Min < 0 || CostRange.Max > 2000)
                errors.Add("成本约束范围超出合理范围(0-2000元/吨)");

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors
            };
        }

        /// <summary>
        /// 检查值是否在约束范围内
        /// </summary>
        /// <param name="componentType">成分类型</param>
        /// <param name="value">值</param>
        /// <returns>是否在范围内</returns>
        public bool IsInRange(ComponentType componentType, double value)
        {
            return componentType switch
            {
                ComponentType.TFe => TFeRange.Contains(value),
                ComponentType.R => RRange.Contains(value),
                ComponentType.MgO => MgORange.Contains(value),
                ComponentType.Al2O3 => Al2O3Range.Contains(value),
                ComponentType.Cost => CostRange.Contains(value),
                _ => false
            };
        }

        /// <summary>
        /// 克隆约束范围对象
        /// </summary>
        /// <returns>克隆的约束范围对象</returns>
        public ConstraintRanges Clone()
        {
            return new ConstraintRanges
            {
                TFeRange = TFeRange.Clone(),
                RRange = RRange.Clone(),
                MgORange = MgORange.Clone(),
                Al2O3Range = Al2O3Range.Clone(),
                CostRange = CostRange.Clone()
            };
        }
    }

    /// <summary>
    /// 成分范围类
    /// </summary>
    public class ComponentRange : INotifyPropertyChanged
    {
        private double _min;
        private double _max;

        public ComponentRange(double min, double max)
        {
            _min = min;
            _max = max;
        }

        /// <summary>
        /// 最小值
        /// </summary>
        public double Min
        {
            get => _min;
            set
            {
                if (Math.Abs(_min - value) > 0.001)
                {
                    _min = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(Range));
                }
            }
        }

        /// <summary>
        /// 最大值
        /// </summary>
        public double Max
        {
            get => _max;
            set
            {
                if (Math.Abs(_max - value) > 0.001)
                {
                    _max = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(Range));
                }
            }
        }

        /// <summary>
        /// 范围大小
        /// </summary>
        public double Range => Max - Min;

        /// <summary>
        /// 中心值
        /// </summary>
        public double Center => (Min + Max) / 2;

        /// <summary>
        /// 检查值是否在范围内
        /// </summary>
        /// <param name="value">值</param>
        /// <returns>是否在范围内</returns>
        public bool Contains(double value)
        {
            return value >= Min && value <= Max;
        }

        /// <summary>
        /// 检查范围是否有效
        /// </summary>
        /// <returns>是否有效</returns>
        public bool IsValid()
        {
            return Min <= Max;
        }

        /// <summary>
        /// 克隆范围对象
        /// </summary>
        /// <returns>克隆的范围对象</returns>
        public ComponentRange Clone()
        {
            return new ComponentRange(Min, Max);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public override bool Equals(object? obj)
        {
            if (obj is ComponentRange other)
            {
                return Math.Abs(Min - other.Min) < 0.001 && Math.Abs(Max - other.Max) < 0.001;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Min, Max);
        }

        public static bool operator ==(ComponentRange? left, ComponentRange? right)
        {
            if (ReferenceEquals(left, right)) return true;
            if (left is null || right is null) return false;
            return left.Equals(right);
        }

        public static bool operator !=(ComponentRange? left, ComponentRange? right)
        {
            return !(left == right);
        }

        public override string ToString()
        {
            return $"{Min:F2} - {Max:F2}";
        }
    }

    /// <summary>
    /// 成分类型枚举
    /// </summary>
    public enum ComponentType
    {
        TFe,
        R,
        MgO,
        Al2O3,
        Cost
    }
}
