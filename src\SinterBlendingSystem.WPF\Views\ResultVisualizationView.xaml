<UserControl x:Class="SinterBlendingSystem.WPF.Views.ResultVisualizationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
    
    <Grid>
        <Border Style="{StaticResource CardStyle}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="ChartPie" Width="64" Height="64" 
                                       Foreground="{StaticResource TextSecondaryBrush}" 
                                       HorizontalAlignment="Center" Margin="0,0,0,16"/>
                <TextBlock Text="结果可视化" Style="{StaticResource TitleTextStyle}" 
                           HorizontalAlignment="Center"/>
                <TextBlock Text="此功能将在后续版本实现" Style="{StaticResource BodyTextStyle}" 
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>


