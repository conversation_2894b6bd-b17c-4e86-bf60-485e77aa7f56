# -*- coding: utf-8 -*-
"""
烧结配料计算模型系统 - SQP优化算法服务
基于SQP二次序列规划算法，支持成本最优和质量最优双目标优化
"""

import sys
import os
import locale
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum

# 设置环境编码
os.environ['PYTHONIOENCODING'] = 'utf-8'
if sys.platform.startswith('win'):
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese (Simplified)_China.utf8')
    except:
        pass

# 导入核心库
from flask import Flask, jsonify, request
try:
    from flask_cors import CORS
    CORS_AVAILABLE = True
except ImportError:
    CORS_AVAILABLE = False
    print("警告: flask_cors未安装，将使用手动CORS设置")

import numpy as np
from scipy.optimize import minimize
import datetime
import time
from functools import wraps

# 优化目标类型枚举
class OptimizationType(Enum):
    COST_OPTIMAL = "cost"
    QUALITY_OPTIMAL = "quality"

# 数据类定义
@dataclass
class MaterialData:
    """原料数据结构"""
    name: str
    tfe: float          # TFe含量(%)
    cao: float          # CaO含量(%)
    sio2: float         # SiO2含量(%)
    mgo: float          # MgO含量(%)
    al2o3: float        # Al2O3含量(%)
    h2o: float          # 水分(%)
    ig: float           # 烧损(%)
    price: float        # 价格(元/吨)
    min_ratio: float = 0.0    # 最小配比(%)
    max_ratio: float = 100.0  # 最大配比(%)

@dataclass
class OptimizationTarget:
    """优化目标参数"""
    tfe: float = 55.0
    r: float = 1.90      # 碱度
    mgo: float = 2.39
    al2o3: float = 1.89

@dataclass
class ConstraintRanges:
    """约束范围"""
    tfe: Tuple[float, float] = (54, 55)
    r: Tuple[float, float] = (1.75, 2.05)
    mgo: Tuple[float, float] = (1.8, 3.0)
    al2o3: Tuple[float, float] = (1.5, 2.5)
    cost: Tuple[float, float] = (600, 680)

@dataclass
class OptimizationResult:
    """优化结果"""
    success: bool
    ratios: Dict[str, float]
    properties: Dict[str, float]
    cost: float
    objective_value: float
    iterations: int
    message: str

# ======================== 核心优化算法类 ========================

class SinterOptimizer:
    """烧结配料SQP优化算法核心类"""

    def __init__(self):
        self.materials: List[MaterialData] = []
        self.targets = OptimizationTarget()
        self.constraints = ConstraintRanges()
        self.optimization_history = []

    def load_materials(self, materials_data: List[Dict]) -> None:
        """加载原料数据"""
        self.materials = []
        for data in materials_data:
            material = MaterialData(
                name=data['name'],
                tfe=data.get('dry_tfe', data.get('tfe', 0)),
                cao=data.get('dry_cao', data.get('cao', 0)),
                sio2=data.get('dry_sio2', data.get('sio2', 0)),
                mgo=data.get('dry_mgo', data.get('mgo', 0)),
                al2o3=data.get('dry_al2o3', data.get('al2o3', 0)),
                h2o=data.get('h2o', 0),
                ig=data.get('ig', 0),
                price=data.get('price', 0),
                min_ratio=data.get('min_ratio', 0),
                max_ratio=data.get('max_ratio', 100)
            )
            self.materials.append(material)

    def convert_wet_to_dry_components(self, wet_components: Dict, h2o: float) -> Dict:
        """湿基成分转干基成分"""
        dry_factor = 1 / (1 - h2o / 100.0)
        return {key: value * dry_factor for key, value in wet_components.items()}

    def calculate_sinter_properties(self, ratios: np.ndarray) -> Tuple[float, float, float, float, float]:
        """计算烧结矿性质"""
        if len(ratios) != len(self.materials):
            raise ValueError("配比数组长度与原料数量不匹配")

        # 转换为小数
        wet_ratios = ratios / 100.0

        # 计算干基质量
        dry_masses = []
        burnt_masses = []
        component_masses = {'TFe': 0, 'CaO': 0, 'SiO2': 0, 'MgO': 0, 'Al2O3': 0}
        total_cost = 0

        for i, material in enumerate(self.materials):
            if wet_ratios[i] > 1e-6:  # 只计算有意义的配比
                dry_mass = wet_ratios[i] * (1 - material.h2o / 100.0)
                burnt_mass = dry_mass * (1 - material.ig / 100.0)

                dry_masses.append(dry_mass)
                burnt_masses.append(burnt_mass)

                # 计算各成分质量
                component_masses['TFe'] += dry_mass * material.tfe / 100.0
                component_masses['CaO'] += dry_mass * material.cao / 100.0
                component_masses['SiO2'] += dry_mass * material.sio2 / 100.0
                component_masses['MgO'] += dry_mass * material.mgo / 100.0
                component_masses['Al2O3'] += dry_mass * material.al2o3 / 100.0

                # 计算成本
                total_cost += dry_mass * material.price

        total_burnt_mass = sum(burnt_masses)
        if total_burnt_mass < 1e-6:
            return 0, 0, 0, 0, 0

        # 计算最终成分百分比
        sinter_tfe = component_masses['TFe'] / total_burnt_mass * 100.0
        sinter_cao = component_masses['CaO'] / total_burnt_mass * 100.0
        sinter_sio2 = component_masses['SiO2'] / total_burnt_mass * 100.0
        sinter_mgo = component_masses['MgO'] / total_burnt_mass * 100.0
        sinter_al2o3 = component_masses['Al2O3'] / total_burnt_mass * 100.0

        # 计算碱度
        sinter_r = sinter_cao / sinter_sio2 if sinter_sio2 > 1e-6 else 999

        # 计算单位成本
        unit_cost = total_cost / total_burnt_mass if total_burnt_mass > 0 else 0

        return sinter_tfe, sinter_r, sinter_mgo, sinter_al2o3, unit_cost

    def create_objective_function(self, optimization_type: OptimizationType):
        """创建目标函数"""
        def objective_function(ratios: np.ndarray) -> float:
            try:
                props = self.calculate_sinter_properties(ratios)
                if props[0] == 0:  # 计算失败
                    return 1e12

                sinter_tfe, sinter_r, sinter_mgo, sinter_al2o3, unit_cost = props

                if optimization_type == OptimizationType.COST_OPTIMAL:
                    # 成本最优：主要优化成本，兼顾质量
                    cost_penalty = unit_cost / 1000.0  # 归一化成本
                    quality_penalty = (
                        0.1 * (sinter_tfe - self.targets.tfe)**2 +
                        0.1 * (sinter_r - self.targets.r)**2 +
                        0.05 * (sinter_mgo - self.targets.mgo)**2 +
                        0.05 * (sinter_al2o3 - self.targets.al2o3)**2
                    )
                    return cost_penalty + quality_penalty

                else:  # QUALITY_OPTIMAL
                    # 质量最优：主要优化质量指标，成本作为约束
                    quality_penalty = (
                        0.5 * (sinter_tfe - self.targets.tfe)**2 +
                        0.3 * (sinter_r - self.targets.r)**2 +
                        0.1 * (sinter_mgo - self.targets.mgo)**2 +
                        0.1 * (sinter_al2o3 - self.targets.al2o3)**2
                    )
                    return quality_penalty

            except Exception:
                return 1e12

        return objective_function

    def create_constraints(self):
        """创建约束条件"""
        constraints = []

        # 配比总和约束
        constraints.append({
            'type': 'eq',
            'fun': lambda x: np.sum(x) - 100.0
        })

        # 成分约束
        def create_component_constraint(component_idx, bound_type, bound_value):
            def constraint_func(x):
                props = self.calculate_sinter_properties(x)
                if props[0] == 0:
                    return -1
                value = props[component_idx]
                return value - bound_value if bound_type == 'min' else bound_value - value
            return constraint_func

        # TFe约束
        constraints.append({
            'type': 'ineq',
            'fun': create_component_constraint(0, 'min', self.constraints.tfe[0])
        })
        constraints.append({
            'type': 'ineq',
            'fun': create_component_constraint(0, 'max', self.constraints.tfe[1])
        })

        # 碱度约束
        constraints.append({
            'type': 'ineq',
            'fun': create_component_constraint(1, 'min', self.constraints.r[0])
        })
        constraints.append({
            'type': 'ineq',
            'fun': create_component_constraint(1, 'max', self.constraints.r[1])
        })

        # MgO约束
        constraints.append({
            'type': 'ineq',
            'fun': create_component_constraint(2, 'min', self.constraints.mgo[0])
        })
        constraints.append({
            'type': 'ineq',
            'fun': create_component_constraint(2, 'max', self.constraints.mgo[1])
        })

        # Al2O3约束
        constraints.append({
            'type': 'ineq',
            'fun': create_component_constraint(3, 'min', self.constraints.al2o3[0])
        })
        constraints.append({
            'type': 'ineq',
            'fun': create_component_constraint(3, 'max', self.constraints.al2o3[1])
        })

        # 成本约束
        constraints.append({
            'type': 'ineq',
            'fun': create_component_constraint(4, 'min', self.constraints.cost[0])
        })
        constraints.append({
            'type': 'ineq',
            'fun': create_component_constraint(4, 'max', self.constraints.cost[1])
        })

        return constraints

    def optimize(self, optimization_type: OptimizationType, max_iterations: int = 10) -> OptimizationResult:
        """执行SQP优化算法"""
        print(f"\n{'='*60}")
        print(f"🔥 开始{optimization_type.value}优化")
        print(f"{'='*60}")

        # 创建目标函数和约束
        objective_func = self.create_objective_function(optimization_type)
        constraints = self.create_constraints()

        # 设置初始值和边界
        n_materials = len(self.materials)
        x0 = np.zeros(n_materials)
        bounds = []

        # 根据原料设置初始值和边界
        total_init = 0
        for i, material in enumerate(self.materials):
            bounds.append((material.min_ratio, material.max_ratio))
            if material.max_ratio > 0:
                x0[i] = (material.min_ratio + material.max_ratio) / 2
                total_init += x0[i]

        # 归一化初始值
        if total_init > 0:
            x0 = x0 / total_init * 100.0

        self.optimization_history = []
        best_result = None

        for iteration in range(max_iterations):
            try:
                print(f"📊 第 {iteration+1} 轮优化迭代...")

                # 执行SQP优化
                result = minimize(
                    objective_func,
                    x0,
                    method='SLSQP',
                    bounds=bounds,
                    constraints=constraints,
                    options={'maxiter': 500, 'ftol': 1e-7, 'disp': False}
                )

                if result.success:
                    # 验证结果
                    props = self.calculate_sinter_properties(result.x)
                    if props[0] > 0:  # 计算成功
                        ratios_dict = {}
                        for i, material in enumerate(self.materials):
                            if result.x[i] > 0.01:  # 只包含有意义的配比
                                ratios_dict[material.name] = float(result.x[i])

                        properties = {
                            'TFe': float(props[0]),
                            'R': float(props[1]),
                            'MgO': float(props[2]),
                            'Al2O3': float(props[3]),
                            'Cost': float(props[4])
                        }

                        best_result = OptimizationResult(
                            success=True,
                            ratios=ratios_dict,
                            properties=properties,
                            cost=float(props[4]),
                            objective_value=float(result.fun),
                            iterations=iteration + 1,
                            message="优化成功"
                        )

                        print(f"✅ 第 {iteration+1} 轮优化成功，目标函数值: {result.fun:.6f}")
                        print(f"🎯 TFe: {props[0]:.2f}%, R: {props[1]:.2f}, 成本: {props[4]:.2f}元/吨")

                        # 检查是否需要调整低配比原料
                        low_ratio_indices = [i for i, ratio in enumerate(result.x)
                                           if 0 < ratio < 2.0 and bounds[i][1] > 0]

                        if not low_ratio_indices:
                            print(f"🎉 优化收敛成功！")
                            break

                        # 调整低配比原料
                        for idx in low_ratio_indices:
                            bounds[idx] = (0, 0)
                            x0[idx] = 0
                            print(f"   - {self.materials[idx].name}: {result.x[idx]:.3f}% -> 0%")

                        # 重新归一化
                        remaining_sum = sum(x0[i] for i in range(n_materials) if bounds[i][1] > 0)
                        if remaining_sum > 0:
                            for i in range(n_materials):
                                if bounds[i][1] > 0:
                                    x0[i] = x0[i] / remaining_sum * 100.0
                    else:
                        print(f"❌ 第 {iteration+1} 轮计算结果无效")
                        break
                else:
                    print(f"❌ 第 {iteration+1} 轮优化失败: {result.message}")
                    if iteration == 0:
                        # 添加随机扰动重试
                        x0 += np.random.uniform(-5, 5, n_materials)
                        x0 = np.clip(x0, [b[0] for b in bounds], [b[1] for b in bounds])
                    else:
                        break

            except Exception as e:
                print(f"💥 优化迭代 {iteration+1} 异常: {e}")
                break

        if best_result is None:
            return OptimizationResult(
                success=False,
                ratios={},
                properties={},
                cost=0,
                objective_value=0,
                iterations=max_iterations,
                message="优化失败，未找到可行解"
            )

        return best_result

# ======================== 工具函数 ========================

def convert_numpy_types(obj):
    """将NumPy类型转换为Python原生类型"""
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, (np.bool_, bool)):
        return bool(obj)
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    return obj

# ======================== Flask应用初始化 ========================

app = Flask(__name__)
app.secret_key = 'sinter-optimization-service'

# CORS设置
if CORS_AVAILABLE:
    CORS(app, origins=['http://localhost:8080', 'http://localhost:3000', 'http://127.0.0.1:3000', 'http://localhost:5000'])
else:
    @app.after_request
    def after_request(response):
        origin = request.headers.get('Origin')
        allowed_origins = ['http://localhost:8080', 'http://localhost:3000', 'http://127.0.0.1:3000']
        if origin in allowed_origins:
            response.headers.add('Access-Control-Allow-Origin', origin)
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
        return response

# 全局优化器实例
optimizer = SinterOptimizer()

# ======================== 默认数据配置 ========================

# 默认原料数据
DEFAULT_MATERIALS = [
    {"name": "碱性精粉", "tfe": 63.76, "cao": 1.94, "sio2": 4.95, "mgo": 1.85, "al2o3": 0.60, "h2o": 8.20, "ig": 1.23, "price": 752.21, "min_ratio": 0, "max_ratio": 30},
    {"name": "酸性精粉", "tfe": 64.89, "cao": 0.70, "sio2": 6.32, "mgo": 0.92, "al2o3": 0.72, "h2o": 9.90, "ig": -0.05, "price": 752.21, "min_ratio": 0, "max_ratio": 30},
    {"name": "海瑞", "tfe": 58.07, "cao": 0.10, "sio2": 6.21, "mgo": 0.28, "al2o3": 2.52, "h2o": 6.00, "ig": 9.07, "price": 822.98, "min_ratio": 0, "max_ratio": 25},
    {"name": "印粉海娜", "tfe": 63.66, "cao": 0.10, "sio2": 4.01, "mgo": 0.24, "al2o3": 2.42, "h2o": 6.70, "ig": 1.60, "price": 832.98, "min_ratio": 15, "max_ratio": 25},
    {"name": "巴西粗粉", "tfe": 64.64, "cao": 0.20, "sio2": 4.69, "mgo": 0.11, "al2o3": 0.73, "h2o": 6.70, "ig": 1.33, "price": 1473.05, "min_ratio": 0, "max_ratio": 20},
    {"name": "俄罗斯精粉", "tfe": 62.95, "cao": 1.71, "sio2": 4.61, "mgo": 3.70, "al2o3": 2.29, "h2o": 10.00, "ig": -0.35, "price": 772.21, "min_ratio": 15, "max_ratio": 25},
    {"name": "高炉返矿", "tfe": 55.54, "cao": 10.60, "sio2": 5.59, "mgo": 2.34, "al2o3": 2.09, "h2o": 0.50, "ig": 1.73, "price": 550.00, "min_ratio": 20, "max_ratio": 30},
    {"name": "回收料", "tfe": 56.16, "cao": 6.56, "sio2": 6.31, "mgo": 2.39, "al2o3": 2.51, "h2o": 10.73, "ig": 1.74, "price": 100.00, "min_ratio": 5, "max_ratio": 10},
    {"name": "钢渣", "tfe": 26.46, "cao": 28.15, "sio2": 15.43, "mgo": 2.79, "al2o3": 2.53, "h2o": 7.60, "ig": 12.05, "price": 550.00, "min_ratio": 2, "max_ratio": 6},
    {"name": "氧化铁皮", "tfe": 69.73, "cao": 0.50, "sio2": 1.50, "mgo": 0.00, "al2o3": 2.88, "h2o": 5.90, "ig": -1.52, "price": 750.00, "min_ratio": 0, "max_ratio": 8},
    {"name": "生石灰", "tfe": 0.00, "cao": 71.74, "sio2": 3.52, "mgo": 2.28, "al2o3": 1.19, "h2o": 7.00, "ig": 16.33, "price": 219.00, "min_ratio": 5, "max_ratio": 8},
    {"name": "轻烧白云石", "tfe": 0.00, "cao": 42.67, "sio2": 5.31, "mgo": 26.12, "al2o3": 0.10, "h2o": 1.50, "ig": 19.73, "price": 183.76, "min_ratio": 2, "max_ratio": 5},
    {"name": "焦粉", "tfe": 0.19, "cao": 0.37, "sio2": 8.82, "mgo": 0.22, "al2o3": 3.31, "h2o": 13.15, "ig": 79.40, "price": 520.00, "min_ratio": 3, "max_ratio": 5},
    {"name": "澳粉纵横", "tfe": 60.80, "cao": 0.10, "sio2": 4.35, "mgo": 0.20, "al2o3": 2.30, "h2o": 8.30, "ig": 6.89, "price": 832.98, "min_ratio": 8, "max_ratio": 15}
]

# 初始化优化器
def initialize_optimizer():
    """初始化优化器"""
    optimizer.load_materials(DEFAULT_MATERIALS)
    optimizer.targets = OptimizationTarget(tfe=55.0, r=1.90, mgo=2.39, al2o3=1.89)
    optimizer.constraints = ConstraintRanges(
        tfe=(54, 56),
        r=(1.88, 2.02),
        mgo=(1.8, 3.0),
        al2o3=(1.5, 2.5),
        cost=(600, 680)
    )

# 初始化
initialize_optimizer()

# ======================== 简化的API接口 ========================

@app.route('/')
def index():
    """主页"""
    return jsonify({
        'message': '烧结配料计算模型系统',
        'version': '2.0.0',
        'status': 'running',
        'endpoints': {
            'solve': '/api/solve',
            'health': '/health'
        }
    })

@app.route('/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.datetime.now().isoformat(),
        'service': 'SQP优化服务'
    })

@app.route('/api/solve', methods=['POST'])
def solve_optimization():
    """
    SQP优化求解接口
    支持成本最优和质量最优双目标优化
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '请求数据为空'}), 400

        # 更新原料数据
        if 'raw_materials' in data:
            optimizer.load_materials(data['raw_materials'])

        # 更新目标参数
        if 'target' in data:
            target_data = data['target']
            optimizer.targets = OptimizationTarget(
                tfe=target_data.get('tfe_target', 55.0),
                r=target_data.get('ro_target', 1.90),
                mgo=target_data.get('mgo_target', 2.39),
                al2o3=target_data.get('al2o3_target', 1.89)
            )

        # 更新约束条件
        if 'constraints' in data:
            constraints_data = data['constraints']
            optimizer.constraints = ConstraintRanges(
                tfe=constraints_data.get('tfe_range', (53.5, 56.5)),
                r=constraints_data.get('ro_range', (1.75, 2.05)),
                mgo=constraints_data.get('mgo_range', (1.8, 3.0)),
                al2o3=constraints_data.get('al2o3_range', (1.5, 2.5)),
                cost=constraints_data.get('cost_range', (600, 665))
            )

        # 确定优化类型
        optimize_type = data.get('optimize_type', 'cost')
        optimization_type = OptimizationType.COST_OPTIMAL if optimize_type == 'cost' else OptimizationType.QUALITY_OPTIMAL

        print(f"\n🔥 开始{optimization_type.value}优化")
        print(f"目标: TFe={optimizer.targets.tfe}, R={optimizer.targets.r}")

        # 执行优化
        result = optimizer.optimize(optimization_type)

        if result.success:
            # 构建返回结果
            response = {
                'success': True,
                'optimization_type': optimize_type,
                'optimal_ratios': result.ratios,
                'sinter_properties': result.properties,
                'unit_cost': result.cost,
                'objective_value': result.objective_value,
                'iterations': result.iterations,
                'message': result.message,
                'timestamp': datetime.datetime.now().isoformat()
            }

            # 如果需要多方案对比，同时计算另一种优化类型
            if data.get('multi_solution', False):
                other_type = OptimizationType.QUALITY_OPTIMAL if optimization_type == OptimizationType.COST_OPTIMAL else OptimizationType.COST_OPTIMAL
                other_result = optimizer.optimize(other_type)

                if other_result.success:
                    response['alternative_solution'] = {
                        'optimization_type': other_type.value,
                        'optimal_ratios': other_result.ratios,
                        'sinter_properties': other_result.properties,
                        'unit_cost': other_result.cost,
                        'objective_value': other_result.objective_value
                    }

            print(f"✅ 优化成功: 成本={result.cost:.2f}元/吨")
            return jsonify(response)
        else:
            return jsonify({
                'success': False,
                'error': result.message,
                'iterations': result.iterations
            }), 400

    except Exception as e:
        print(f"❌ 优化过程异常: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}'
        }), 500

# ======================== 应用启动 ========================

if __name__ == '__main__':
    print("=" * 60)
    print("🔥 烧结配料计算模型系统启动中...")
    print(f"📡 服务地址: http://localhost:5000")
    print(f"🔍 主要接口: http://localhost:5000/api/solve")
    print(f"❤️ 健康检查: http://localhost:5000/health")
    print(f"📊 已加载 {len(optimizer.materials)} 种原料")
    print("=" * 60)

    # 启动Flask应用
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
