<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- 数据网格样式 -->
    <Style x:Key="MaterialDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HorizontalGridLinesBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="VerticalGridLinesBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="RowBackground" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="AlternatingRowBackground" Value="{StaticResource GridAlternateRowBrush}"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="SelectionUnit" Value="FullRow"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="CanUserReorderColumns" Value="True"/>
        <Setter Property="CanUserResizeColumns" Value="True"/>
        <Setter Property="CanUserResizeRows" Value="False"/>
        <Setter Property="CanUserSortColumns" Value="True"/>
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="RowHeight" Value="32"/>
    </Style>

    <!-- 数据网格列标题样式 -->
    <Style x:Key="MaterialDataGridColumnHeaderStyle" TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
        <Setter Property="Background" Value="{StaticResource GridHeaderBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="0,0,1,1"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
    </Style>

    <!-- 数据网格行样式 -->
    <Style x:Key="MaterialDataGridRowStyle" TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Margin" Value="0"/>
        <Setter Property="Padding" Value="0"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource GridHoverRowBrush}"/>
            </Trigger>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Background" Value="{StaticResource GridSelectedRowBrush}"/>
                <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 数据网格单元格样式 -->
    <Style x:Key="MaterialDataGridCellStyle" TargetType="DataGridCell" BasedOn="{StaticResource MaterialDesignDataGridCell}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="FontSize" Value="12"/>
        <Style.Triggers>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 数值列样式 -->
    <Style x:Key="NumericColumnStyle" TargetType="DataGridTextColumn">
        <Setter Property="ElementStyle">
            <Setter.Value>
                <Style TargetType="TextBlock">
                    <Setter Property="HorizontalAlignment" Value="Right"/>
                    <Setter Property="VerticalAlignment" Value="Center"/>
                    <Setter Property="Padding" Value="8,4"/>
                </Style>
            </Setter.Value>
        </Setter>
        <Setter Property="EditingElementStyle">
            <Setter.Value>
                <Style TargetType="TextBox">
                    <Setter Property="HorizontalAlignment" Value="Stretch"/>
                    <Setter Property="VerticalAlignment" Value="Center"/>
                    <Setter Property="HorizontalContentAlignment" Value="Right"/>
                    <Setter Property="Padding" Value="8,4"/>
                    <Setter Property="BorderThickness" Value="0"/>
                    <Setter Property="Background" Value="Transparent"/>
                </Style>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 文本列样式 -->
    <Style x:Key="TextColumnStyle" TargetType="DataGridTextColumn">
        <Setter Property="ElementStyle">
            <Setter.Value>
                <Style TargetType="TextBlock">
                    <Setter Property="HorizontalAlignment" Value="Left"/>
                    <Setter Property="VerticalAlignment" Value="Center"/>
                    <Setter Property="Padding" Value="8,4"/>
                    <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                </Style>
            </Setter.Value>
        </Setter>
        <Setter Property="EditingElementStyle">
            <Setter.Value>
                <Style TargetType="TextBox">
                    <Setter Property="HorizontalAlignment" Value="Stretch"/>
                    <Setter Property="VerticalAlignment" Value="Center"/>
                    <Setter Property="Padding" Value="8,4"/>
                    <Setter Property="BorderThickness" Value="0"/>
                    <Setter Property="Background" Value="Transparent"/>
                </Style>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 只读列样式 -->
    <Style x:Key="ReadOnlyColumnStyle" TargetType="DataGridTextColumn" BasedOn="{StaticResource TextColumnStyle}">
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="ElementStyle">
            <Setter.Value>
                <Style TargetType="TextBlock">
                    <Setter Property="HorizontalAlignment" Value="Left"/>
                    <Setter Property="VerticalAlignment" Value="Center"/>
                    <Setter Property="Padding" Value="8,4"/>
                    <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
                    <Setter Property="FontStyle" Value="Italic"/>
                </Style>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
