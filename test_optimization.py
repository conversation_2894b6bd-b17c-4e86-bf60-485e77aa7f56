#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化服务的脚本
"""

import requests
import json
import time

def test_health():
    """测试健康检查"""
    try:
        response = requests.get('http://localhost:5000/health')
        print(f"健康检查状态: {response.status_code}")
        print(f"响应内容: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_optimization():
    """测试优化计算"""
    # 构建测试数据
    test_data = {
        "materials": [
            {
                "name": "澳粉",
                "type": "IronOre",
                "tfe_wet": 62.5,
                "sio2_wet": 4.2,
                "al2o3_wet": 2.1,
                "cao_wet": 0.8,
                "mgo_wet": 0.5,
                "h2o": 8.5,
                "cost": 850.0,
                "min_ratio": 0.0,
                "max_ratio": 40.0
            },
            {
                "name": "巴粉",
                "type": "IronOre", 
                "tfe_wet": 65.2,
                "sio2_wet": 3.8,
                "al2o3_wet": 1.9,
                "cao_wet": 0.6,
                "mgo_wet": 0.4,
                "h2o": 9.2,
                "cost": 920.0,
                "min_ratio": 0.0,
                "max_ratio": 35.0
            },
            {
                "name": "石灰石",
                "type": "Flux",
                "tfe_wet": 2.0,
                "sio2_wet": 8.5,
                "al2o3_wet": 1.2,
                "cao_wet": 48.5,
                "mgo_wet": 1.8,
                "h2o": 2.0,
                "cost": 120.0,
                "min_ratio": 8.0,
                "max_ratio": 15.0
            }
        ],
        "target": {
            "tfe": 58.0,
            "sio2": 5.5,
            "cao": 8.5,
            "mgo": 1.5,
            "al2o3": 2.0,
            "optimization_type": "CostOptimal"
        },
        "constraints": {
            "tfe_min": 56.0,
            "tfe_max": 60.0,
            "sio2_min": 4.5,
            "sio2_max": 6.5,
            "cao_min": 7.5,
            "cao_max": 9.5,
            "mgo_min": 1.0,
            "mgo_max": 2.0,
            "al2o3_min": 1.5,
            "al2o3_max": 2.5,
            "basicity_min": 1.3,
            "basicity_max": 1.8
        }
    }
    
    try:
        print("发送优化请求...")
        response = requests.post(
            'http://localhost:5000/api/solve',
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("优化成功!")
            print(f"成功状态: {result.get('success', False)}")
            
            if result.get('success'):
                print("最优配比:")
                for material, ratio in result.get('optimal_ratios', {}).items():
                    print(f"  {material}: {ratio:.2f}%")
                
                print(f"总成本: {result.get('total_cost', 0):.2f} 元/吨")
                
                properties = result.get('mixed_properties', {})
                print("混合料性质:")
                print(f"  TFe: {properties.get('tfe', 0):.2f}%")
                print(f"  SiO2: {properties.get('sio2', 0):.2f}%")
                print(f"  CaO: {properties.get('cao', 0):.2f}%")
                print(f"  MgO: {properties.get('mgo', 0):.2f}%")
                print(f"  碱度: {properties.get('basicity', 0):.2f}")
            else:
                print(f"优化失败: {result.get('message', '未知错误')}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"优化测试失败: {e}")

if __name__ == "__main__":
    print("=" * 50)
    print("烧结配料优化服务测试")
    print("=" * 50)
    
    # 测试健康检查
    print("\n1. 测试健康检查...")
    if test_health():
        print("✓ 健康检查通过")
        
        # 测试优化计算
        print("\n2. 测试优化计算...")
        test_optimization()
    else:
        print("✗ 健康检查失败，请确保服务正在运行")
    
    print("\n测试完成!")
