@echo off
echo ========================================
echo 烧结配料系统启动脚本
echo ========================================

echo.
echo 1. 启动Python优化服务...
cd /d "C:\Users\<USER>\Desktop\730"
start "Python优化服务" cmd /k "cd py && python optimization_service.py"

echo.
echo 2. 等待服务启动...
timeout /t 3 /nobreak >nul

echo.
echo 3. 测试服务连接...
python test_optimization.py

echo.
echo 4. 启动WPF应用程序...
start "WPF应用程序" cmd /k "dotnet run --project src/SinterBlendingSystem.WPF"

echo.
echo ========================================
echo 系统启动完成！
echo ========================================
echo.
echo Python服务地址: http://localhost:5000
echo 健康检查: http://localhost:5000/health
echo.
echo 按任意键退出...
pause >nul
