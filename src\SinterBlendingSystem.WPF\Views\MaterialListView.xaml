<UserControl x:Class="SinterBlendingSystem.WPF.Views.MaterialListView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="原料管理" Style="{StaticResource TitleTextStyle}"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="添加原料" Command="{Binding AddMaterialCommand}" 
                            Style="{StaticResource MaterialDesignRaisedAccentButton}" Margin="4"/>
                    <Button Content="保存" Command="{Binding SaveMaterialsCommand}" 
                            Style="{StaticResource MaterialDesignRaisedButton}" Margin="4"/>
                    <Button Content="重置" Command="{Binding ResetToDefaultCommand}" 
                            Style="{StaticResource MaterialDesignOutlinedButton}" Margin="4"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 搜索和过滤栏 -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,0,0,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBox Grid.Column="0" Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" 
                         materialDesign:HintAssist.Hint="搜索原料..." 
                         Style="{StaticResource MaterialTextBoxStyle}" Margin="0,0,16,0"/>
                
                <CheckBox Grid.Column="1" Content="显示锁定原料" 
                          IsChecked="{Binding ShowLockedMaterials}" 
                          VerticalAlignment="Center"/>
            </Grid>
        </Border>

        <!-- 原料数据表格 -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <DataGrid ItemsSource="{Binding Materials}" 
                      SelectedItem="{Binding SelectedMaterial}"
                      AutoGenerateColumns="False" 
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      SelectionMode="Single"
                      IsReadOnly="False">
                
                <DataGrid.Columns>
                    <!-- 原料代码 -->
                    <DataGridTextColumn Header="代码" Binding="{Binding Code}" Width="80" IsReadOnly="True"/>
                    
                    <!-- 原料名称 -->
                    <DataGridTextColumn Header="名称" Binding="{Binding Name}" Width="120"/>
                    
                    <!-- TFe含量 -->
                    <DataGridTextColumn Header="TFe(%)" Binding="{Binding TFeWet, StringFormat=F2}" Width="80"/>
                    
                    <!-- SiO2含量 -->
                    <DataGridTextColumn Header="SiO2(%)" Binding="{Binding SiO2Wet, StringFormat=F2}" Width="80"/>
                    
                    <!-- CaO含量 -->
                    <DataGridTextColumn Header="CaO(%)" Binding="{Binding CaOWet, StringFormat=F2}" Width="80"/>
                    
                    <!-- MgO含量 -->
                    <DataGridTextColumn Header="MgO(%)" Binding="{Binding MgOWet, StringFormat=F2}" Width="80"/>
                    
                    <!-- Al2O3含量 -->
                    <DataGridTextColumn Header="Al2O3(%)" Binding="{Binding Al2O3Wet, StringFormat=F2}" Width="80"/>
                    
                    <!-- 水分 -->
                    <DataGridTextColumn Header="H2O(%)" Binding="{Binding H2O, StringFormat=F2}" Width="80"/>
                    
                    <!-- 烧损 -->
                    <DataGridTextColumn Header="Ig(%)" Binding="{Binding Ig, StringFormat=F2}" Width="80"/>
                    
                    <!-- 价格 -->
                    <DataGridTextColumn Header="价格(元/吨)" Binding="{Binding Price, StringFormat=F2}" Width="100"/>
                    
                    <!-- 最小配比 -->
                    <DataGridTextColumn Header="最小配比(%)" Binding="{Binding MinRatio, StringFormat=F2}" Width="100"/>
                    
                    <!-- 最大配比 -->
                    <DataGridTextColumn Header="最大配比(%)" Binding="{Binding MaxRatio, StringFormat=F2}" Width="100"/>
                    
                    <!-- 当前配比 -->
                    <DataGridTextColumn Header="当前配比(%)" Binding="{Binding CurrentRatio, StringFormat=F2}" Width="100"/>
                    
                    <!-- 状态 -->
                    <DataGridTemplateColumn Header="状态" Width="80">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Ellipse Width="12" Height="12" VerticalAlignment="Center" HorizontalAlignment="Center" Fill="Green"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- 操作按钮栏 -->
        <Border Grid.Row="3" Style="{StaticResource CardStyle}" Margin="0,8,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="编辑" Command="{Binding EditMaterialCommand}" 
                        Style="{StaticResource MaterialDesignRaisedButton}" Margin="4"/>
                <Button Content="复制" Command="{Binding CopyMaterialCommand}" 
                        Style="{StaticResource MaterialDesignRaisedButton}" Margin="4"/>
                <Button Content="删除" Command="{Binding DeleteMaterialCommand}" 
                        Style="{StaticResource MaterialDesignRaisedSecondaryButton}" Margin="4"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
