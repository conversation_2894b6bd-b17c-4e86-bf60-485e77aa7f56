using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using SinterBlendingSystem.Core.Interfaces;
using SinterBlendingSystem.Core.Models;
using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;

namespace SinterBlendingSystem.WPF.ViewModels
{
    /// <summary>
    /// 结果显示ViewModel
    /// </summary>
    public class ResultDisplayViewModel : ViewModelBase
    {
        private readonly IDataService _dataService;
        private readonly IOptimizationService _optimizationService;

        private ObservableCollection<OptimizationResult> _historyResults = new();
        private OptimizationResult? _selectedResult;
        private OptimizationResult? _currentResult;

        public ResultDisplayViewModel(
            ILogger logger, 
            IDataService dataService, 
            IOptimizationService optimizationService) : base(logger)
        {
            _dataService = dataService;
            _optimizationService = optimizationService;
            InitializeCommands();
        }

        #region 属性

        /// <summary>
        /// 历史结果列表
        /// </summary>
        public ObservableCollection<OptimizationResult> HistoryResults
        {
            get => _historyResults;
            set => SetProperty(ref _historyResults, value);
        }

        /// <summary>
        /// 选中的历史结果
        /// </summary>
        public OptimizationResult? SelectedResult
        {
            get => _selectedResult;
            set
            {
                SetProperty(ref _selectedResult, value);
                OnPropertyChanged(nameof(HasSelectedResult));
                // 更新命令状态
                DeleteResultCommand.NotifyCanExecuteChanged();
            }
        }

        /// <summary>
        /// 当前优化结果
        /// </summary>
        public OptimizationResult? CurrentResult
        {
            get => _currentResult;
            set => SetProperty(ref _currentResult, value);
        }

        /// <summary>
        /// 是否有选中的结果
        /// </summary>
        public bool HasSelectedResult => SelectedResult != null;

        /// <summary>
        /// 是否有当前结果
        /// </summary>
        public bool HasCurrentResult => CurrentResult != null;

        #endregion

        #region 命令

        public IAsyncRelayCommand LoadHistoryCommand { get; private set; } = null!;
        public IAsyncRelayCommand DeleteResultCommand { get; private set; } = null!;
        public IRelayCommand RefreshCommand { get; private set; } = null!;

        #endregion

        #region 初始化

        private void InitializeCommands()
        {
            LoadHistoryCommand = new AsyncRelayCommand(LoadHistoryAsync);
            DeleteResultCommand = new AsyncRelayCommand(DeleteResultAsync);
            RefreshCommand = new RelayCommand(Refresh);
        }

        public override async Task InitializeAsync()
        {
            await base.InitializeAsync();
            await LoadHistoryAsync();
        }

        #endregion

        #region 历史记录操作

        private async Task LoadHistoryAsync()
        {
            await ExecuteAsync(async () =>
            {
                var pagedResult = await _dataService.GetOptimizationHistoryAsync(0, 50);
                
                HistoryResults.Clear();
                foreach (var result in pagedResult.Items)
                {
                    HistoryResults.Add(result);
                }

                _logger.LogInformation("已加载{Count}条历史记录", HistoryResults.Count);
            }, "正在加载历史记录...", "加载历史记录失败");
        }

        private async Task DeleteResultAsync()
        {
            if (SelectedResult == null) return;

            await ExecuteAsync(async () =>
            {
                var success = await _dataService.DeleteOptimizationResultAsync(SelectedResult.Timestamp);
                if (success)
                {
                    HistoryResults.Remove(SelectedResult);
                    SelectedResult = null;
                    _logger.LogInformation("删除历史记录成功");
                }
                else
                {
                    throw new InvalidOperationException("删除历史记录失败");
                }
            }, "正在删除记录...", "删除记录失败");
        }



        private void Refresh()
        {
            _ = LoadHistoryAsync();
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 设置当前结果
        /// </summary>
        /// <param name="result">优化结果</param>
        public void SetCurrentResult(OptimizationResult result)
        {
            CurrentResult = result;
            OnPropertyChanged(nameof(HasCurrentResult));
        }

        /// <summary>
        /// 获取结果摘要信息
        /// </summary>
        /// <param name="result">优化结果</param>
        /// <returns>摘要信息</returns>
        public string GetResultSummary(OptimizationResult result)
        {
            if (result == null) return "无结果";

            if (!result.Success)
            {
                return $"优化失败：{result.Message}";
            }

            return $"成本：{result.UnitCost:F2}元/吨，TFe：{result.SinterProperties.TFe:F2}%，碱度：{result.SinterProperties.R:F2}";
        }

        /// <summary>
        /// 比较两个结果
        /// </summary>
        /// <param name="result1">结果1</param>
        /// <param name="result2">结果2</param>
        /// <returns>比较结果</returns>
        public string CompareResults(OptimizationResult result1, OptimizationResult result2)
        {
            if (result1 == null || result2 == null) return "无法比较";

            var costDiff = result2.UnitCost - result1.UnitCost;
            var tfeDiff = result2.SinterProperties.TFe - result1.SinterProperties.TFe;
            var rDiff = result2.SinterProperties.R - result1.SinterProperties.R;

            return $"成本差异：{costDiff:+F2;-F2}元/吨，TFe差异：{tfeDiff:+F2;-F2}%，碱度差异：{rDiff:+F2;-F2}";
        }

        #endregion
    }
}
