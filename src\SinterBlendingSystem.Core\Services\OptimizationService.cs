using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SinterBlendingSystem.Core.Interfaces;
using SinterBlendingSystem.Core.Models;
using System.Diagnostics;
using System.Text;

namespace SinterBlendingSystem.Core.Services
{
    /// <summary>
    /// 优化服务实现
    /// </summary>
    public class OptimizationService : IOptimizationService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<OptimizationService> _logger;
        private readonly string _pythonServiceUrl;

        public event EventHandler<OptimizationProgressEventArgs>? ProgressChanged;
        public event EventHandler<OptimizationCompletedEventArgs>? OptimizationCompleted;

        public OptimizationService(HttpClient httpClient, ILogger<OptimizationService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _pythonServiceUrl = "http://localhost:5000"; // Python服务地址
            
            // 配置HttpClient
            _httpClient.Timeout = TimeSpan.FromMinutes(5); // 5分钟超时
        }

        public async Task<OptimizationResult> OptimizeAsync(
            IEnumerable<MaterialData> materials,
            OptimizationTarget target,
            ConstraintRanges constraints,
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                _logger.LogInformation("开始优化计算，优化类型：{OptimizationType}", target.OptimizationType);

                // 验证参数
                var validation = ValidateParameters(materials, target, constraints);
                if (!validation.IsValid)
                {
                    return new OptimizationResult
                    {
                        Success = false,
                        Message = $"参数验证失败：{string.Join(", ", validation.Errors)}",
                        OptimizationType = target.OptimizationType
                    };
                }

                // 构建请求数据
                var requestData = BuildOptimizationRequest(materials, target, constraints);
                
                // 发送优化请求
                var result = await SendOptimizationRequestAsync(requestData, cancellationToken);
                
                stopwatch.Stop();
                result.CalculationTime = stopwatch.ElapsedMilliseconds;

                // 触发完成事件
                OptimizationCompleted?.Invoke(this, new OptimizationCompletedEventArgs
                {
                    Result = result,
                    ElapsedTime = stopwatch.ElapsedMilliseconds,
                    IsCancelled = cancellationToken.IsCancellationRequested
                });

                _logger.LogInformation("优化计算完成，耗时：{ElapsedTime}ms，成功：{Success}", 
                    stopwatch.ElapsedMilliseconds, result.Success);

                return result;
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("优化计算被取消");
                return new OptimizationResult
                {
                    Success = false,
                    Message = "优化计算被取消",
                    OptimizationType = target.OptimizationType
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "优化计算发生异常");
                return new OptimizationResult
                {
                    Success = false,
                    Message = $"优化计算异常：{ex.Message}",
                    OptimizationType = target.OptimizationType
                };
            }
        }

        public async Task<MultiSolutionResult> OptimizeMultiSolutionAsync(
            IEnumerable<MaterialData> materials,
            OptimizationTarget target,
            ConstraintRanges constraints,
            CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("开始多方案优化计算");

            var result = new MultiSolutionResult();

            try
            {
                // 成本最优方案
                var costTarget = target.Clone();
                costTarget.OptimizationType = OptimizationType.CostOptimal;
                result.CostOptimalSolution = await OptimizeAsync(materials, costTarget, constraints, cancellationToken);

                // 质量最优方案
                var qualityTarget = target.Clone();
                qualityTarget.OptimizationType = OptimizationType.QualityOptimal;
                result.QualityOptimalSolution = await OptimizeAsync(materials, qualityTarget, constraints, cancellationToken);

                _logger.LogInformation("多方案优化完成，成本最优：{CostSuccess}，质量最优：{QualitySuccess}",
                    result.CostOptimalSolution?.Success, result.QualityOptimalSolution?.Success);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "多方案优化计算发生异常");
            }

            return result;
        }

        public ValidationResult ValidateParameters(
            IEnumerable<MaterialData> materials,
            OptimizationTarget target,
            ConstraintRanges constraints)
        {
            var errors = new List<string>();

            // 验证原料数据
            var materialList = materials.ToList();
            if (!materialList.Any())
            {
                errors.Add("原料数据不能为空");
            }
            else
            {
                // 检查是否有有效的配比范围
                var validMaterials = materialList.Where(m => m.MaxRatio > 0).ToList();
                if (!validMaterials.Any())
                {
                    errors.Add("至少需要一种原料的最大配比大于0");
                }

                // 检查原料数据完整性
                foreach (var material in materialList)
                {
                    if (string.IsNullOrWhiteSpace(material.Name))
                        errors.Add($"原料名称不能为空");
                    
                    if (material.MinRatio > material.MaxRatio)
                        errors.Add($"原料{material.Name}的最小配比不能大于最大配比");
                    
                    if (material.H2O >= 100)
                        errors.Add($"原料{material.Name}的水分不能大于等于100%");
                }
            }

            // 验证优化目标
            var targetValidation = target.Validate();
            if (!targetValidation.IsValid)
            {
                errors.AddRange(targetValidation.Errors);
            }

            // 验证约束条件
            var constraintValidation = constraints.Validate();
            if (!constraintValidation.IsValid)
            {
                errors.AddRange(constraintValidation.Errors);
            }

            return new ValidationResult
            {
                IsValid = errors.Count == 0,
                Errors = errors
            };
        }

        public SinterProperties CalculateMixedProperties(IEnumerable<MaterialData> materials)
        {
            var materialList = materials.ToList();
            var totalRatio = materialList.Sum(m => m.CurrentRatio);

            if (totalRatio <= 0)
            {
                return new SinterProperties();
            }

            // 计算干基质量和烧成质量
            double totalDryMass = 0;
            double totalBurntMass = 0;
            double totalTFe = 0, totalCaO = 0, totalSiO2 = 0, totalMgO = 0, totalAl2O3 = 0;

            foreach (var material in materialList)
            {
                if (material.CurrentRatio <= 0) continue;

                var wetRatio = material.CurrentRatio / 100.0;
                var dryMass = wetRatio * (1 - material.H2O / 100.0);
                var burntMass = dryMass * (1 - material.Ig / 100.0);

                totalDryMass += dryMass;
                totalBurntMass += burntMass;

                totalTFe += dryMass * material.TFeDry / 100.0;
                totalCaO += dryMass * material.CaODry / 100.0;
                totalSiO2 += dryMass * material.SiO2Dry / 100.0;
                totalMgO += dryMass * material.MgODry / 100.0;
                totalAl2O3 += dryMass * material.Al2O3Dry / 100.0;
            }

            if (totalBurntMass <= 0)
            {
                return new SinterProperties();
            }

            var properties = new SinterProperties
            {
                TFe = totalTFe / totalBurntMass * 100.0,
                CaO = totalCaO / totalBurntMass * 100.0,
                SiO2 = totalSiO2 / totalBurntMass * 100.0,
                MgO = totalMgO / totalBurntMass * 100.0,
                Al2O3 = totalAl2O3 / totalBurntMass * 100.0
            };

            // 计算碱度
            properties.R = properties.SiO2 > 0.001 ? properties.CaO / properties.SiO2 : 999;

            return properties;
        }

        public double CalculateUnitCost(IEnumerable<MaterialData> materials)
        {
            var materialList = materials.ToList();
            var totalRatio = materialList.Sum(m => m.CurrentRatio);

            if (totalRatio <= 0)
            {
                return 0;
            }

            double totalCost = 0;
            double totalDryMass = 0;

            foreach (var material in materialList)
            {
                if (material.CurrentRatio <= 0) continue;

                var wetRatio = material.CurrentRatio / 100.0;
                var dryMass = wetRatio * (1 - material.H2O / 100.0);

                totalDryMass += dryMass;
                totalCost += dryMass * material.Price;
            }

            return totalDryMass > 0 ? totalCost / totalDryMass : 0;
        }

        public async Task<ServiceHealthStatus> CheckHealthAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var response = await _httpClient.GetAsync($"{_pythonServiceUrl}/health");
                stopwatch.Stop();

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var healthData = JsonConvert.DeserializeObject<dynamic>(content);
                    
                    return new ServiceHealthStatus
                    {
                        IsHealthy = true,
                        Message = "服务正常",
                        ResponseTime = stopwatch.ElapsedMilliseconds,
                        Version = healthData?.service?.ToString() ?? "未知"
                    };
                }
                else
                {
                    return new ServiceHealthStatus
                    {
                        IsHealthy = false,
                        Message = $"服务响应异常：{response.StatusCode}",
                        ResponseTime = stopwatch.ElapsedMilliseconds
                    };
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                return new ServiceHealthStatus
                {
                    IsHealthy = false,
                    Message = $"服务连接失败：{ex.Message}",
                    ResponseTime = stopwatch.ElapsedMilliseconds
                };
            }
        }

        private object BuildOptimizationRequest(
            IEnumerable<MaterialData> materials,
            OptimizationTarget target,
            ConstraintRanges constraints)
        {
            var materialList = materials.Where(m => m.MaxRatio > 0).ToList();

            return new
            {
                raw_materials = materialList.Select(m => new
                {
                    name = m.Name,
                    dry_tfe = m.TFeDry,
                    dry_cao = m.CaODry,
                    dry_sio2 = m.SiO2Dry,
                    dry_mgo = m.MgODry,
                    dry_al2o3 = m.Al2O3Dry,
                    h2o = m.H2O,
                    ig = m.Ig,
                    price = m.Price,
                    min_ratio = m.MinRatio,
                    max_ratio = m.MaxRatio
                }),
                target = new
                {
                    tfe_target = target.TFe,
                    ro_target = target.R,
                    mgo_target = target.MgO,
                    al2o3_target = target.Al2O3
                },
                constraints = new
                {
                    tfe_range = new[] { constraints.TFeRange.Min, constraints.TFeRange.Max },
                    ro_range = new[] { constraints.RRange.Min, constraints.RRange.Max },
                    mgo_range = new[] { constraints.MgORange.Min, constraints.MgORange.Max },
                    al2o3_range = new[] { constraints.Al2O3Range.Min, constraints.Al2O3Range.Max },
                    cost_range = new[] { constraints.CostRange.Min, constraints.CostRange.Max }
                },
                optimize_type = target.OptimizationType == OptimizationType.CostOptimal ? "cost" : "quality",
                multi_solution = false
            };
        }

        private async Task<OptimizationResult> SendOptimizationRequestAsync(
            object requestData,
            CancellationToken cancellationToken)
        {
            var json = JsonConvert.SerializeObject(requestData, Formatting.Indented);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            _logger.LogDebug("发送优化请求：{RequestData}", json);

            // 发送进度更新
            ProgressChanged?.Invoke(this, new OptimizationProgressEventArgs
            {
                ProgressPercentage = 10,
                StatusMessage = "正在发送优化请求..."
            });

            var response = await _httpClient.PostAsync($"{_pythonServiceUrl}/api/solve", content, cancellationToken);

            ProgressChanged?.Invoke(this, new OptimizationProgressEventArgs
            {
                ProgressPercentage = 50,
                StatusMessage = "正在计算优化方案..."
            });

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("优化请求失败：{StatusCode}, {Content}", response.StatusCode, errorContent);
                
                return new OptimizationResult
                {
                    Success = false,
                    Message = $"优化请求失败：{response.StatusCode} - {errorContent}"
                };
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            _logger.LogDebug("收到优化响应：{ResponseContent}", responseContent);

            ProgressChanged?.Invoke(this, new OptimizationProgressEventArgs
            {
                ProgressPercentage = 90,
                StatusMessage = "正在解析优化结果..."
            });

            return ParseOptimizationResponse(responseContent);
        }

        private OptimizationResult ParseOptimizationResponse(string responseContent)
        {
            try
            {
                var responseData = JsonConvert.DeserializeObject<dynamic>(responseContent);

                if (responseData?.success != true)
                {
                    return new OptimizationResult
                    {
                        Success = false,
                        Message = responseData?.error?.ToString() ?? "优化失败"
                    };
                }

                var result = new OptimizationResult
                {
                    Success = true,
                    Message = responseData.message?.ToString() ?? "优化成功",
                    OptimizationType = responseData.optimization_type?.ToString() == "cost" 
                        ? OptimizationType.CostOptimal 
                        : OptimizationType.QualityOptimal,
                    ObjectiveValue = (double)(responseData.objective_value ?? 0),
                    Iterations = (int)(responseData.iterations ?? 0),
                    UnitCost = (double)(responseData.unit_cost ?? 0),
                    Timestamp = DateTime.Now
                };

                // 解析配比结果
                if (responseData.optimal_ratios != null)
                {
                    result.OptimalRatios = JsonConvert.DeserializeObject<Dictionary<string, double>>(
                        responseData.optimal_ratios.ToString());
                }

                // 解析烧结矿性质
                if (responseData.sinter_properties != null)
                {
                    var properties = responseData.sinter_properties;
                    result.SinterProperties = new SinterProperties
                    {
                        TFe = (double)(properties.TFe ?? 0),
                        R = (double)(properties.R ?? 0),
                        MgO = (double)(properties.MgO ?? 0),
                        Al2O3 = (double)(properties.Al2O3 ?? 0),
                        CaO = (double)(properties.CaO ?? 0),
                        SiO2 = (double)(properties.SiO2 ?? 0)
                    };
                }

                ProgressChanged?.Invoke(this, new OptimizationProgressEventArgs
                {
                    ProgressPercentage = 100,
                    StatusMessage = "优化完成"
                });

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析优化响应失败：{ResponseContent}", responseContent);
                return new OptimizationResult
                {
                    Success = false,
                    Message = $"解析优化结果失败：{ex.Message}"
                };
            }
        }
    }
}
