using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using SinterBlendingSystem.Core.Interfaces;
using SinterBlendingSystem.Core.Models;
using SinterBlendingSystem.WPF.Views;
using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;

namespace SinterBlendingSystem.WPF.ViewModels
{
    /// <summary>
    /// 主窗口ViewModel
    /// </summary>
    public class MainWindowViewModel : ViewModelBase
    {
        private readonly IDataService _dataService;
        private readonly IOptimizationService _optimizationService;
        private readonly DispatcherTimer _timer;
        private readonly DispatcherTimer _healthCheckTimer;

        private UserControl? _currentView;
        private string _statusMessage = "就绪";
        private DateTime _currentTime = DateTime.Now;
        private bool _isOptimizing;
        private int _optimizationProgress;
        private ServiceHealthStatus _serviceStatus = new() { IsHealthy = false };
        private string _serviceStatusText = "未连接";

        // 子ViewModels
        private MaterialListViewModel? _materialListViewModel;
        private OptimizationConfigViewModel? _optimizationConfigViewModel;
        private ResultDisplayViewModel? _resultDisplayViewModel;

        public MainWindowViewModel(
            ILogger<MainWindowViewModel> logger,
            IDataService dataService,
            IOptimizationService optimizationService) : base(logger)
        {
            _dataService = dataService;
            _optimizationService = optimizationService;

            // 初始化定时器
            _timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _timer.Tick += Timer_Tick;
            _timer.Start();

            // 初始化健康检查定时器
            _healthCheckTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(10) // 每10秒检查一次
            };
            _healthCheckTimer.Tick += async (s, e) => await CheckServiceHealthAsync();
            _healthCheckTimer.Start();

            // 订阅优化服务事件
            _optimizationService.ProgressChanged += OnOptimizationProgressChanged;
            _optimizationService.OptimizationCompleted += OnOptimizationCompleted;

            // 初始化命令
            InitializeCommands();

            // 默认显示原料列表
            _ = NavigateToAsync("MaterialList");
        }

        #region 属性

        /// <summary>
        /// 当前视图
        /// </summary>
        public UserControl? CurrentView
        {
            get => _currentView;
            set => SetProperty(ref _currentView, value);
        }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// 当前时间
        /// </summary>
        public DateTime CurrentTime
        {
            get => _currentTime;
            set => SetProperty(ref _currentTime, value);
        }

        /// <summary>
        /// 是否正在优化
        /// </summary>
        public bool IsOptimizing
        {
            get => _isOptimizing;
            set => SetProperty(ref _isOptimizing, value);
        }

        /// <summary>
        /// 优化进度
        /// </summary>
        public int OptimizationProgress
        {
            get => _optimizationProgress;
            set => SetProperty(ref _optimizationProgress, value);
        }

        /// <summary>
        /// 服务状态
        /// </summary>
        public ServiceHealthStatus ServiceStatus
        {
            get => _serviceStatus;
            set
            {
                if (SetProperty(ref _serviceStatus, value))
                {
                    // 当服务状态改变时，通知OptimizeCommand刷新CanExecute状态
                    OptimizeCommand?.NotifyCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// 服务状态文本
        /// </summary>
        public string ServiceStatusText
        {
            get => _serviceStatusText;
            set => SetProperty(ref _serviceStatusText, value);
        }

        #endregion

        #region 命令

        public IAsyncRelayCommand<string> NavigateCommand { get; private set; } = null!;
        public IAsyncRelayCommand OptimizeCommand { get; private set; } = null!;
        public IAsyncRelayCommand SaveCommand { get; private set; } = null!;
        public IAsyncRelayCommand LoadCommand { get; private set; } = null!;
        public IRelayCommand SettingsCommand { get; private set; } = null!;

        #endregion

        #region 初始化

        private void InitializeCommands()
        {
            NavigateCommand = new AsyncRelayCommand<string>(NavigateToAsync);
            OptimizeCommand = new AsyncRelayCommand(ExecuteOptimizationAsync, CanExecuteOptimization);
            SaveCommand = new AsyncRelayCommand(SaveConfigurationAsync);
            LoadCommand = new AsyncRelayCommand(LoadConfigurationAsync);
            SettingsCommand = new RelayCommand(ShowSettings);
        }

        public override async Task InitializeAsync()
        {
            await base.InitializeAsync();
            
            // 检查服务健康状态
            await CheckServiceHealthAsync();
            
            StatusMessage = "系统初始化完成";
        }

        #endregion

        #region 导航

        private async Task NavigateToAsync(string? viewName)
        {
            if (string.IsNullOrEmpty(viewName))
                return;

            try
            {
                UserControl? view = null;

                switch (viewName)
                {
                    case "MaterialList":
                        view = await CreateMaterialListViewAsync();
                        break;
                    case "MaterialEdit":
                        view = await CreateMaterialEditViewAsync();
                        break;
                    case "TargetConfig":
                        view = await CreateTargetConfigViewAsync();
                        break;
                    case "ConstraintConfig":
                        view = await CreateConstraintConfigViewAsync();
                        break;
                    case "ResultComparison":
                        view = await CreateResultComparisonViewAsync();
                        break;
                    case "ResultVisualization":
                        view = await CreateResultVisualizationViewAsync();
                        break;
                    case "History":
                        view = await CreateHistoryViewAsync();
                        break;
                    case "ImportExport":
                        view = await CreateImportExportViewAsync();
                        break;
                    case "SystemSettings":
                        view = await CreateSystemSettingsViewAsync();
                        break;
                    default:
                        _logger.LogWarning("未知的视图名称：{ViewName}", viewName);
                        return;
                }

                if (view != null)
                {
                    CurrentView = view;
                    StatusMessage = $"已切换到：{GetViewDisplayName(viewName)}";
                }
            }
            catch (Exception ex)
            {
                HandleException(ex, $"导航到{viewName}失败");
            }
        }

        private async Task<UserControl?> CreateMaterialListViewAsync()
        {
            if (_materialListViewModel == null)
            {
                _materialListViewModel = new MaterialListViewModel(_logger, _dataService);
                await _materialListViewModel.InitializeAsync();
            }

            return new MaterialListView { DataContext = _materialListViewModel };
        }

        private async Task<UserControl?> CreateMaterialEditViewAsync()
        {
            // 暂时返回原料列表视图
            return await CreateMaterialListViewAsync();
        }

        private async Task<UserControl?> CreateTargetConfigViewAsync()
        {
            if (_optimizationConfigViewModel == null)
            {
                _optimizationConfigViewModel = new OptimizationConfigViewModel(_logger, _dataService);
                await _optimizationConfigViewModel.InitializeAsync();
            }

            return new TargetConfigView { DataContext = _optimizationConfigViewModel };
        }

        private async Task<UserControl?> CreateConstraintConfigViewAsync()
        {
            if (_optimizationConfigViewModel == null)
            {
                _optimizationConfigViewModel = new OptimizationConfigViewModel(_logger, _dataService);
                await _optimizationConfigViewModel.InitializeAsync();
            }

            return new ConstraintConfigView { DataContext = _optimizationConfigViewModel };
        }

        private async Task<UserControl?> CreateResultComparisonViewAsync()
        {
            if (_resultDisplayViewModel == null)
            {
                _resultDisplayViewModel = new ResultDisplayViewModel(_logger, _dataService, _optimizationService);
                await _resultDisplayViewModel.InitializeAsync();
            }

            return new ResultComparisonView { DataContext = _resultDisplayViewModel };
        }

        private async Task<UserControl?> CreateResultVisualizationViewAsync()
        {
            if (_resultDisplayViewModel == null)
            {
                _resultDisplayViewModel = new ResultDisplayViewModel(_logger, _dataService, _optimizationService);
                await _resultDisplayViewModel.InitializeAsync();
            }

            return new ResultVisualizationView { DataContext = _resultDisplayViewModel };
        }

        private async Task<UserControl?> CreateHistoryViewAsync()
        {
            if (_resultDisplayViewModel == null)
            {
                _resultDisplayViewModel = new ResultDisplayViewModel(_logger, _dataService, _optimizationService);
                await _resultDisplayViewModel.InitializeAsync();
            }

            return new HistoryView { DataContext = _resultDisplayViewModel };
        }

        private async Task<UserControl?> CreateImportExportViewAsync()
        {
            // 暂时返回空视图
            return new UserControl();
        }

        private async Task<UserControl?> CreateSystemSettingsViewAsync()
        {
            // 暂时返回空视图
            return new UserControl();
        }

        private string GetViewDisplayName(string viewName)
        {
            return viewName switch
            {
                "MaterialList" => "原料列表",
                "MaterialEdit" => "原料编辑",
                "TargetConfig" => "目标配置",
                "ConstraintConfig" => "约束配置",
                "ResultComparison" => "方案对比",
                "ResultVisualization" => "结果可视化",
                "History" => "历史记录",
                "ImportExport" => "导入导出",
                "SystemSettings" => "系统设置",
                _ => viewName
            };
        }

        #endregion

        #region 优化操作

        private async Task ExecuteOptimizationAsync()
        {
            await ExecuteAsync(async () =>
            {
                // 获取原料数据
                var materials = await _dataService.GetMaterialsAsync();
                var target = await _dataService.GetOptimizationTargetAsync();
                var constraints = await _dataService.GetConstraintRangesAsync();

                // 执行优化
                IsOptimizing = true;
                var result = await _optimizationService.OptimizeAsync(materials, target, constraints);

                // 保存结果
                if (result.Success)
                {
                    await _dataService.SaveOptimizationResultAsync(result);
                    StatusMessage = "优化计算完成";
                    
                    // 显示结果
                    await NavigateToAsync("ResultComparison");
                }
                else
                {
                    StatusMessage = $"优化失败：{result.Message}";
                }
            }, "正在执行优化计算...", "优化计算失败");
        }

        private bool CanExecuteOptimization()
        {
            return !IsOptimizing && ServiceStatus.IsHealthy;
        }

        #endregion

        #region 配置操作

        private async Task SaveConfigurationAsync()
        {
            await ExecuteAsync(async () =>
            {
                // 保存当前配置
                if (_optimizationConfigViewModel != null)
                {
                    await _optimizationConfigViewModel.SaveConfigurationAsync();
                }

                StatusMessage = "配置已保存";
            }, "正在保存配置...", "保存配置失败");
        }

        private async Task LoadConfigurationAsync()
        {
            await ExecuteAsync(async () =>
            {
                // 重新加载配置
                if (_optimizationConfigViewModel != null)
                {
                    await _optimizationConfigViewModel.LoadConfigurationAsync();
                }

                StatusMessage = "配置已加载";
            }, "正在加载配置...", "加载配置失败");
        }

        #endregion

        #region 设置

        private void ShowSettings()
        {
            // 显示设置对话框
            MessageBox.Show("设置功能将在后续版本实现", "设置", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion

        #region 事件处理

        private void Timer_Tick(object? sender, EventArgs e)
        {
            CurrentTime = DateTime.Now;
        }

        private void OnOptimizationProgressChanged(object? sender, OptimizationProgressEventArgs e)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                OptimizationProgress = e.ProgressPercentage;
                StatusMessage = e.StatusMessage;
            });
        }

        private void OnOptimizationCompleted(object? sender, OptimizationCompletedEventArgs e)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                IsOptimizing = false;
                OptimizationProgress = 0;
                
                if (e.Result.Success)
                {
                    StatusMessage = $"优化完成，耗时：{e.ElapsedTime:F0}ms";
                }
                else
                {
                    StatusMessage = $"优化失败：{e.Result.Message}";
                }

                // 刷新命令状态
                OptimizeCommand.NotifyCanExecuteChanged();
            });
        }

        private async Task CheckServiceHealthAsync()
        {
            try
            {
                var status = await _optimizationService.CheckHealthAsync();
                ServiceStatus = status;
                ServiceStatusText = status.IsHealthy ? "已连接" : "未连接";
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "检查服务健康状态失败");
                ServiceStatus = new ServiceHealthStatus { IsHealthy = false, Message = ex.Message };
                ServiceStatusText = "连接失败";
            }
        }

        #endregion

        #region 清理

        public override void Cleanup()
        {
            _timer?.Stop();
            _healthCheckTimer?.Stop();

            _optimizationService.ProgressChanged -= OnOptimizationProgressChanged;
            _optimizationService.OptimizationCompleted -= OnOptimizationCompleted;

            _materialListViewModel?.Cleanup();
            _optimizationConfigViewModel?.Cleanup();
            _resultDisplayViewModel?.Cleanup();

            base.Cleanup();
        }

        #endregion
    }
}
