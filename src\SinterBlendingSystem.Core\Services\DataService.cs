using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SinterBlendingSystem.Core.Interfaces;
using SinterBlendingSystem.Core.Models;
using System.Text;

namespace SinterBlendingSystem.Core.Services
{
    /// <summary>
    /// 数据服务实现
    /// </summary>
    public class DataService : IDataService
    {
        private readonly ILogger<DataService> _logger;
        private readonly string _dataDirectory;
        private readonly string _materialsFile;
        private readonly string _targetFile;
        private readonly string _constraintsFile;
        private readonly string _historyDirectory;

        public event EventHandler<DataChangedEventArgs>? DataChanged;

        public DataService(ILogger<DataService> logger)
        {
            _logger = logger;
            _dataDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                "SinterBlendingSystem");
            _materialsFile = Path.Combine(_dataDirectory, "materials.json");
            _targetFile = Path.Combine(_dataDirectory, "target.json");
            _constraintsFile = Path.Combine(_dataDirectory, "constraints.json");
            _historyDirectory = Path.Combine(_dataDirectory, "history");

            // 确保目录存在
            Directory.CreateDirectory(_dataDirectory);
            Directory.CreateDirectory(_historyDirectory);
        }

        public async Task<IEnumerable<MaterialData>> GetMaterialsAsync()
        {
            try
            {
                if (!File.Exists(_materialsFile))
                {
                    _logger.LogInformation("原料数据文件不存在，返回默认数据");
                    return await GetDefaultMaterialsAsync();
                }

                var json = await File.ReadAllTextAsync(_materialsFile, Encoding.UTF8);
                var materials = JsonConvert.DeserializeObject<List<MaterialData>>(json) ?? new List<MaterialData>();
                
                _logger.LogDebug("成功加载{Count}条原料数据", materials.Count);
                return materials;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载原料数据失败");
                return await GetDefaultMaterialsAsync();
            }
        }

        public async Task<MaterialData?> GetMaterialByCodeAsync(string code)
        {
            var materials = await GetMaterialsAsync();
            return materials.FirstOrDefault(m => m.Code.Equals(code, StringComparison.OrdinalIgnoreCase));
        }

        public async Task<bool> SaveMaterialAsync(MaterialData material)
        {
            try
            {
                var materials = (await GetMaterialsAsync()).ToList();
                var existingIndex = materials.FindIndex(m => m.Code.Equals(material.Code, StringComparison.OrdinalIgnoreCase));

                if (existingIndex >= 0)
                {
                    materials[existingIndex] = material;
                    _logger.LogInformation("更新原料数据：{Code} - {Name}", material.Code, material.Name);
                }
                else
                {
                    materials.Add(material);
                    _logger.LogInformation("添加原料数据：{Code} - {Name}", material.Code, material.Name);
                }

                await SaveMaterialsAsync(materials);

                // 触发数据变更事件
                DataChanged?.Invoke(this, new DataChangedEventArgs
                {
                    ChangeType = existingIndex >= 0 ? DataChangeType.Updated : DataChangeType.Added,
                    DataType = typeof(MaterialData),
                    DataId = material.Code,
                    Description = $"{(existingIndex >= 0 ? "更新" : "添加")}原料：{material.Name}"
                });

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存原料数据失败：{Code}", material.Code);
                return false;
            }
        }

        public async Task<bool> SaveMaterialsAsync(IEnumerable<MaterialData> materials)
        {
            try
            {
                var json = JsonConvert.SerializeObject(materials, Formatting.Indented);
                await File.WriteAllTextAsync(_materialsFile, json, Encoding.UTF8);
                
                _logger.LogInformation("成功保存{Count}条原料数据", materials.Count());

                // 触发数据变更事件
                DataChanged?.Invoke(this, new DataChangedEventArgs
                {
                    ChangeType = DataChangeType.BatchOperation,
                    DataType = typeof(MaterialData),
                    Description = $"批量保存{materials.Count()}条原料数据"
                });

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量保存原料数据失败");
                return false;
            }
        }

        public async Task<bool> DeleteMaterialAsync(string code)
        {
            try
            {
                var materials = (await GetMaterialsAsync()).ToList();
                var material = materials.FirstOrDefault(m => m.Code.Equals(code, StringComparison.OrdinalIgnoreCase));

                if (material == null)
                {
                    _logger.LogWarning("要删除的原料不存在：{Code}", code);
                    return false;
                }

                if (material.IsLocked)
                {
                    _logger.LogWarning("无法删除锁定的原料：{Code}", code);
                    return false;
                }

                materials.Remove(material);
                await SaveMaterialsAsync(materials);

                _logger.LogInformation("删除原料数据：{Code} - {Name}", material.Code, material.Name);

                // 触发数据变更事件
                DataChanged?.Invoke(this, new DataChangedEventArgs
                {
                    ChangeType = DataChangeType.Deleted,
                    DataType = typeof(MaterialData),
                    DataId = code,
                    Description = $"删除原料：{material.Name}"
                });

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除原料数据失败：{Code}", code);
                return false;
            }
        }

        public async Task<bool> DeleteMaterialsAsync(IEnumerable<string> codes)
        {
            try
            {
                var materials = (await GetMaterialsAsync()).ToList();
                var deletedCount = 0;

                foreach (var code in codes)
                {
                    var material = materials.FirstOrDefault(m => m.Code.Equals(code, StringComparison.OrdinalIgnoreCase));
                    if (material != null && !material.IsLocked)
                    {
                        materials.Remove(material);
                        deletedCount++;
                    }
                }

                if (deletedCount > 0)
                {
                    await SaveMaterialsAsync(materials);
                    _logger.LogInformation("批量删除{Count}条原料数据", deletedCount);

                    // 触发数据变更事件
                    DataChanged?.Invoke(this, new DataChangedEventArgs
                    {
                        ChangeType = DataChangeType.BatchOperation,
                        DataType = typeof(MaterialData),
                        Description = $"批量删除{deletedCount}条原料数据"
                    });
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量删除原料数据失败");
                return false;
            }
        }

        public async Task<OptimizationTarget> GetOptimizationTargetAsync()
        {
            try
            {
                if (!File.Exists(_targetFile))
                {
                    _logger.LogInformation("优化目标文件不存在，返回默认配置");
                    return new OptimizationTarget();
                }

                var json = await File.ReadAllTextAsync(_targetFile, Encoding.UTF8);
                var target = JsonConvert.DeserializeObject<OptimizationTarget>(json) ?? new OptimizationTarget();
                
                _logger.LogDebug("成功加载优化目标配置");
                return target;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载优化目标配置失败");
                return new OptimizationTarget();
            }
        }

        public async Task<bool> SaveOptimizationTargetAsync(OptimizationTarget target)
        {
            try
            {
                var json = JsonConvert.SerializeObject(target, Formatting.Indented);
                await File.WriteAllTextAsync(_targetFile, json, Encoding.UTF8);
                
                _logger.LogInformation("成功保存优化目标配置");

                // 触发数据变更事件
                DataChanged?.Invoke(this, new DataChangedEventArgs
                {
                    ChangeType = DataChangeType.Updated,
                    DataType = typeof(OptimizationTarget),
                    Description = "更新优化目标配置"
                });

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存优化目标配置失败");
                return false;
            }
        }

        public async Task<ConstraintRanges> GetConstraintRangesAsync()
        {
            try
            {
                if (!File.Exists(_constraintsFile))
                {
                    _logger.LogInformation("约束条件文件不存在，返回默认配置");
                    return new ConstraintRanges();
                }

                var json = await File.ReadAllTextAsync(_constraintsFile, Encoding.UTF8);
                var constraints = JsonConvert.DeserializeObject<ConstraintRanges>(json) ?? new ConstraintRanges();
                
                _logger.LogDebug("成功加载约束条件配置");
                return constraints;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载约束条件配置失败");
                return new ConstraintRanges();
            }
        }

        public async Task<bool> SaveConstraintRangesAsync(ConstraintRanges constraints)
        {
            try
            {
                var json = JsonConvert.SerializeObject(constraints, Formatting.Indented);
                await File.WriteAllTextAsync(_constraintsFile, json, Encoding.UTF8);
                
                _logger.LogInformation("成功保存约束条件配置");

                // 触发数据变更事件
                DataChanged?.Invoke(this, new DataChangedEventArgs
                {
                    ChangeType = DataChangeType.Updated,
                    DataType = typeof(ConstraintRanges),
                    Description = "更新约束条件配置"
                });

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存约束条件配置失败");
                return false;
            }
        }

        public async Task<PagedResult<OptimizationResult>> GetOptimizationHistoryAsync(int pageIndex = 0, int pageSize = 20)
        {
            try
            {
                var historyFiles = Directory.GetFiles(_historyDirectory, "*.json")
                    .OrderByDescending(f => File.GetCreationTime(f))
                    .ToList();

                var totalCount = historyFiles.Count;
                var pagedFiles = historyFiles.Skip(pageIndex * pageSize).Take(pageSize);

                var results = new List<OptimizationResult>();
                foreach (var file in pagedFiles)
                {
                    try
                    {
                        var json = await File.ReadAllTextAsync(file, Encoding.UTF8);
                        var result = JsonConvert.DeserializeObject<OptimizationResult>(json);
                        if (result != null)
                        {
                            results.Add(result);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "读取历史记录文件失败：{File}", file);
                    }
                }

                return new PagedResult<OptimizationResult>
                {
                    Items = results,
                    TotalCount = totalCount,
                    PageIndex = pageIndex,
                    PageSize = pageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取优化历史记录失败");
                return new PagedResult<OptimizationResult>();
            }
        }

        public async Task<bool> SaveOptimizationResultAsync(OptimizationResult result)
        {
            try
            {
                var fileName = $"result_{result.Timestamp:yyyyMMdd_HHmmss}.json";
                var filePath = Path.Combine(_historyDirectory, fileName);

                var json = JsonConvert.SerializeObject(result, Formatting.Indented);
                await File.WriteAllTextAsync(filePath, json, Encoding.UTF8);
                
                _logger.LogInformation("成功保存优化结果：{FileName}", fileName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存优化结果失败");
                return false;
            }
        }

        public async Task<bool> DeleteOptimizationResultAsync(DateTime timestamp)
        {
            try
            {
                var fileName = $"result_{timestamp:yyyyMMdd_HHmmss}.json";
                var filePath = Path.Combine(_historyDirectory, fileName);

                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    _logger.LogInformation("删除优化结果：{FileName}", fileName);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除优化结果失败：{Timestamp}", timestamp);
                return false;
            }
        }

        public async Task<bool> ClearOptimizationHistoryAsync()
        {
            try
            {
                var historyFiles = Directory.GetFiles(_historyDirectory, "*.json");
                foreach (var file in historyFiles)
                {
                    File.Delete(file);
                }

                _logger.LogInformation("清空优化历史记录，删除{Count}个文件", historyFiles.Length);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清空优化历史记录失败");
                return false;
            }
        }

        public async Task<IEnumerable<MaterialData>> GetDefaultMaterialsAsync()
        {
            // 返回默认的原料数据
            return await Task.FromResult(new List<MaterialData>
            {
                new MaterialData
                {
                    Code = "A001",
                    Name = "澳洲粉矿",
                    TFeWet = 61.5,
                    SiO2Wet = 4.8,
                    CaOWet = 0.08,
                    MgOWet = 0.05,
                    Al2O3Wet = 2.1,
                    H2O = 8.5,
                    Ig = -0.5,
                    Price = 680,
                    MinRatio = 0,
                    MaxRatio = 40,
                    IsLocked = true,
                    Status = MaterialStatus.Normal
                },
                new MaterialData
                {
                    Code = "B001",
                    Name = "巴西粉矿",
                    TFeWet = 64.2,
                    SiO2Wet = 2.1,
                    CaOWet = 0.02,
                    MgOWet = 0.03,
                    Al2O3Wet = 1.8,
                    H2O = 9.2,
                    Ig = 1.2,
                    Price = 720,
                    MinRatio = 0,
                    MaxRatio = 35,
                    IsLocked = true,
                    Status = MaterialStatus.Normal
                },
                new MaterialData
                {
                    Code = "C001",
                    Name = "石灰石",
                    TFeWet = 1.2,
                    SiO2Wet = 2.8,
                    CaOWet = 52.5,
                    MgOWet = 1.2,
                    Al2O3Wet = 0.8,
                    H2O = 1.5,
                    Ig = 42.8,
                    Price = 120,
                    MinRatio = 8,
                    MaxRatio = 15,
                    IsLocked = true,
                    Status = MaterialStatus.Normal
                },
                new MaterialData
                {
                    Code = "D001",
                    Name = "白云石",
                    TFeWet = 0.8,
                    SiO2Wet = 1.2,
                    CaOWet = 30.2,
                    MgOWet = 20.8,
                    Al2O3Wet = 0.5,
                    H2O = 0.8,
                    Ig = 46.2,
                    Price = 150,
                    MinRatio = 0,
                    MaxRatio = 8,
                    IsLocked = true,
                    Status = MaterialStatus.Normal
                }
            });
        }

        public async Task<bool> ResetToDefaultDataAsync()
        {
            try
            {
                var defaultMaterials = await GetDefaultMaterialsAsync();
                await SaveMaterialsAsync(defaultMaterials);

                var defaultTarget = new OptimizationTarget();
                await SaveOptimizationTargetAsync(defaultTarget);

                var defaultConstraints = new ConstraintRanges();
                await SaveConstraintRangesAsync(defaultConstraints);

                _logger.LogInformation("成功重置为默认数据");

                // 触发数据变更事件
                DataChanged?.Invoke(this, new DataChangedEventArgs
                {
                    ChangeType = DataChangeType.Reset,
                    DataType = typeof(object),
                    Description = "重置为默认数据"
                });

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置为默认数据失败");
                return false;
            }
        }

        // 导入导出功能将在后续实现
        public Task<ImportResult> ImportMaterialsFromExcelAsync(string filePath)
        {
            throw new NotImplementedException("Excel导入功能将在后续版本实现");
        }

        public Task<bool> ExportMaterialsToExcelAsync(string filePath, IEnumerable<MaterialData> materials)
        {
            throw new NotImplementedException("Excel导出功能将在后续版本实现");
        }

        public Task<bool> ExportOptimizationResultToExcelAsync(string filePath, OptimizationResult result)
        {
            throw new NotImplementedException("Excel导出功能将在后续版本实现");
        }
    }
}
