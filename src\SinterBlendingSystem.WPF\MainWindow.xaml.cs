using SinterBlendingSystem.WPF.ViewModels;
using System.Windows;

namespace SinterBlendingSystem.WPF
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow(MainWindowViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;

            // 异步初始化ViewModel
            Loaded += async (s, e) => await viewModel.InitializeAsync();
        }
    }
}
